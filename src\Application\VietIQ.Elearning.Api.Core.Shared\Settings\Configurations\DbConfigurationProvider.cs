﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Shared.Settings
{
    public class DbConfigurationProvider : ConfigurationProvider
    {
        private readonly Action<DbContextOptionsBuilder> _optionsAction;

        public DbConfigurationProvider(Action<DbContextOptionsBuilder> optionsAction)
        {
            _optionsAction = optionsAction;
        }

        public override void Load()
        {
            if (IsInMigrationMode() || !ShouldUseDbSettings())
            {
                Console.WriteLine("Skipping DB settings (migration mode or disabled in config).");
                return;
            }

            var builder = new DbContextOptionsBuilder<ApplicationDbContext>();
            _optionsAction(builder);

            try
            {
                using var dbContext = new ApplicationDbContext(builder.Options, new NullHttpContextAccessor());
                if (!dbContext.Database.CanConnect())
                {
                    Console.WriteLine("Cannot connect to database.");
                    return;
                }

                if (!dbContext.Database.GetService<IRelationalDatabaseCreator>().Exists())
                {
                    Console.WriteLine("Database does not exist.");
                    return;
                }

                if (dbContext.Model.FindEntityType(typeof(Setting)) == null)
                {
                    Console.WriteLine("Entity 'Setting' is not found in DbContext.");
                    return;
                }

                var dbSetSetting = dbContext.Set<Setting>();
                if (dbSetSetting != null && dbSetSetting.Any())
                {
                    Data = dbSetSetting.ToDictionary(c => c.Key, c => c.Value, StringComparer.OrdinalIgnoreCase);
                }
            }
            catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "42P01")
            {
                Console.WriteLine("Skipping DB settings: Settings table does not exist yet.");
            }
            catch (Exception ex)
            {
                Console.WriteLine("DbConfigurationProvider load failed:");
                Console.WriteLine(ex.ToString());
                if (ex.InnerException != null)
                {
                    Console.WriteLine("Inner exception:");
                    Console.WriteLine(ex.InnerException.ToString());
                }
            }
        }

        private bool ShouldUseDbSettings()
        {
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";

            var config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile($"appsettings.{env}.json", optional: true)
                .Build();

            return bool.TryParse(config["UseDbSettings"], out var result) && result;
        }

        private static bool IsInMigrationMode()
        {
            return AppDomain.CurrentDomain.GetAssemblies().Any(a => a.FullName?.Contains("EntityFrameworkCore.Tools") == true);
        }

        private class NullHttpContextAccessor : IHttpContextAccessor
        {
            public HttpContext? HttpContext { get => null; set { } }
        }
    }
}
