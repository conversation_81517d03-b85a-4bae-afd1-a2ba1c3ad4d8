﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DistributedLock.Redis" Version="1.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\VietIQ.Elearning.DynamicApi\VietIQ.Elearning.DynamicApi.csproj" />
    <ProjectReference Include="..\..\Core\VietIQ.Elearning.EntityFramework\VietIQ.Elearning.EntityFramework.csproj" />
    <ProjectReference Include="..\..\Core\VietIQ.Elearning.Infrastructure\VietIQ.Elearning.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Core\VietIQ.Elearning.JwtBearer\VietIQ.Elearning.JwtBearer.csproj" />
  </ItemGroup>

</Project>
