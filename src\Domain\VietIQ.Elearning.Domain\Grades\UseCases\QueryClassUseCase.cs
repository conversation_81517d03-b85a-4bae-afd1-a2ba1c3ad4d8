﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.Domain.Classes;
using VietIQ.Elearning.Domain.Courses;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Grades
{
    public class QueryGradeUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IQueryGradeUseCase
    {
        public async Task<IApiResponse> GetAsync(IPaginationInput input)
        {
            var repo = GetGenericRepository<Grade>();
            var predicate = PredicateBuilder.False<Grade>();
            if (input.HasSearchTerm)
            {
                predicate = predicate.Or(x => EF.Functions.Like(x.Name, input.SearchTermPattern));
            }

            var result = await repo.AsQueryable().WhereIf(input.HasSearchTerm, predicate).ToPaginationAsync(input);
            return MapPagination<Grade, GradeModel>(result).ToApiResponse();
        }

        public async Task<IApiResponse> GetByIdAsync(int id)
        {
            var repo = GetGenericRepository<Grade, int>();
            var entity = await repo.FindAsync(x => x.Id == id);
            if (entity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Grade.GradeNotFound);
            }

            return ApiResponse.Ok(Mapper.Map<GradeModel>(entity));
        }

        public async Task<IApiResponse> GetClassesByGradeIdAsync(int gradeId)
        {
            var gradeRepo = GetGenericRepository<Grade>();
            var grade = await gradeRepo.FindAsync(x => x.Id == gradeId);
            if (grade == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Class.ClassNotFound);
            }

            var classRepo = GetGenericRepository<Class>();
            var classes = await classRepo.FindAsync(x => x.GradeId == gradeId);
            return ApiResponse.Ok(Mapper.Map<IEnumerator<ClassModel>>(classes));
        }

        public async Task<IApiResponse> GetCoursesByGradeIdAsync(int gradeId)
        {
            var gradeRepo = GetGenericRepository<Grade>();
            var grade = await gradeRepo.FindAsync(x => x.Id == gradeId);
            if (grade == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Grade.GradeNotFound);
            }

            var courseRepo = GetGenericRepository<Course>();
            var courses = await courseRepo.FindAsync(x => x.GradeId == gradeId);
            return ApiResponse.Ok(Mapper.Map<IEnumerator<CourseModel>>(courses));
        }
    }
}
