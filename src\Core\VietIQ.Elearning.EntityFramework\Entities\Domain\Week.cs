﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Weeks")]
    public class Week : Entity<int>
    {
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public int CourseId { get; set; }
        public int WeekNumber { get; set; }
        public string? LearningObjectives { get; set; }

        public virtual Course Course { get; set; } = null!;
        public virtual ICollection<Lesson> Lessons { get; set; } = [];
    }
}
