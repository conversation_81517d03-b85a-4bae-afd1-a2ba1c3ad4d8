﻿using VietIQ.Elearning.Domain.ExerciseAnswers;
using VietIQ.Elearning.Domain.MediaFiles;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.ExerciseItems
{
    public class UpdateExerciseItemInput : IExerciseItemInput
    {
        public DisplayDirection DisplayDirection { get; set; } = DisplayDirection.Vertical;
        public DisplayTypeEnum DisplayType { get; set; } = DisplayTypeEnum.Text;
        public string QuestionText { get; set; } = null!;
        public string? ExpectedAnswerText { get; set; }
        public string? SentenceOrderingJson { get; set; }
        public List<MatchingPairInput>? MatchingPairs { get; set; }
        public string? Explanation { get; set; }

        public List<AddMediaFileInput> Medias { get; set; } = [];
        public List<AddExerciseAnswerInput> Answers { get; set; } = [];
    }
}
