﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities.Domain;

namespace VietIQ.Elearning.Domain.PronunciationResults
{
    public class PronunciationResultManager : BaseService, IPronunciationResultManager
    {
        private readonly IConfiguration configuration;
        public PronunciationResultManager(IUnitOfWork unitOfWork, IMapper mapper, IConfiguration configuration) : base(unitOfWork, mapper)
        {
            this.configuration = configuration;
        }

        public async Task<IApiResponse> GetPronunciationResultByIdAsync(string requestId, int currentUserId)
        {
            var resultRepo = GetGenericRepository<PronunciationResult>();
            var result = await resultRepo.FindAsync(r => r.RequestId == requestId);
            if (result == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            var model = Mapper.Map<PronunciationResultModel>(result);

            if (currentUserId != model.UserId)
            {
                return ApiResponse.Unauthorized(MessageDefinition.NotAllowed);
            }

            return ApiResponse.Ok(result, MessageDefinition.Succeeded);
        }

        public async Task<IApiResponse> ReceivePronunciationResultCallbackAsync(ReceivePronunciationResultInput resultInput)
        {
            resultInput.SecretKey = configuration.GetValue<string>(SettingKey.FCloud.SecretKey) ?? string.Empty;
            if (!ValidateSignature(resultInput.Input, resultInput.SecretKey, resultInput.Signature))
            {
                return ApiResponse.Unauthorized("Invalid signature");
            }

            string requestId = resultInput.Input && resultInput.Input.request_id?.ToString();
            if (string.IsNullOrEmpty(requestId))
            {
                return ApiResponse.BadRequest(nameof(resultInput.Input.request_id), "Missing request_id");
            }

            var repo = GetGenericRepository<PronunciationResult>();
            var existingResult = await repo.FindAsync(x => x.RequestId == requestId)
                                      ?? new PronunciationResult { RequestId = requestId };

            var extraData = resultInput.Input.extra_data?.ToString();

            var metadata = !string.IsNullOrEmpty(extraData) ? JsonSerializer.Deserialize<CallbackMetadata>(extraData) : null;
            if (metadata == null)
            {
                return ApiResponse.BadRequest(nameof(metadata), "Invalid or missing extra_data");
            }

            existingResult.ExerciseId = metadata.ExerciseId;
            existingResult.UserId = metadata.UserId;

            var ts = resultInput.Input.ts?.ToObject<long>();
            existingResult.CreatedTime = DateTimeOffset.FromUnixTimeMilliseconds(ts ?? 0).UtcDateTime;

            // Xử lý webhook
            if (resultInput.Input.request != null && resultInput.Input.response != null) // HookStart hoặc HookStop
            {
                if (resultInput.Input.record_audio_url != null) // HookStop
                {
                    existingResult.Status = "completed";
                    existingResult.Score = resultInput.Input.accuracy_score?.ToObject<int>();
                    existingResult.Feedback = JsonSerializer.Serialize(resultInput.Input.words_score_detail?.Take(1));
                    existingResult.AudioFileUrl = resultInput.Input.record_audio_url?.ToString() ?? resultInput.Input.minio_link?.ToString();
                    existingResult.ProcessingTime = resultInput.Input.processing_time?.ToObject<double>();
                    existingResult.WordsPerMin = resultInput.Input.words_per_min?.ToObject<double>();
                    existingResult.SpeakingDuration = resultInput.Input.speaking_duration?.ToObject<double>();
                    existingResult.UserPhoneme = resultInput.Input.user_phoneme?.ToString();
                    existingResult.WordsScoreDetail = JsonSerializer.Serialize(resultInput.Input.words_score_detail);
                    existingResult.PhonemeScoreStatistics = JsonSerializer.Serialize(resultInput.Input.phoneme_score_statistics);
                    existingResult.StressResponse = JsonSerializer.Serialize(resultInput.Input.stress_response);
                }
                else // HookStart
                {
                    existingResult.Status = "started";
                }
            }
            else if (resultInput.Input.message != null && resultInput.Input.code != null) // HookInterrupt
            {
                existingResult.Status = "interrupted";
                existingResult.ErrorCode = resultInput.Input.code?.ToObject<int>();
                existingResult.ErrorMessage = resultInput.Input.message?.ToString();
            }
            else
            {
                return ApiResponse.BadRequest(nameof(resultInput), "Unknown callback type");
            }

            if (existingResult.Id == 0)
            {
                await repo.AddAsync(existingResult);
            }
            else
            {
                repo.Update(existingResult);
            }

            await CommitAsync();
            return ApiResponse.Ok(MessageDefinition.Succeeded);
        }

        private static bool ValidateSignature(dynamic input, string secretKey, string signature)
        {
            if (string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(secretKey))
            {
                return false;
            }

            var ts = input.ts?.ToString();
            var requestId = input.request_id?.ToString();
            var appId = input.app_id?.ToString();
            var serviceId = input.service_id?.ToString();
            var data = $"request_id={requestId}&app_id={appId}&service_id={serviceId}&ts={ts}";
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            var computedSignature = BitConverter.ToString(hash).Replace("-", "").ToLower();
            return signature.Equals(computedSignature);
        }
    }
}
