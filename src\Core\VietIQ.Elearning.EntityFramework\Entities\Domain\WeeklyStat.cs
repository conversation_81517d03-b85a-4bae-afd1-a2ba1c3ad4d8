﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("WeeklyStats")]
    public class WeeklyStat : Entity<int>
    {
        public int? UserId { get; set; }
        public DateTime WeekStart { get; set; }
        public int LessonCompleted { get; set; }
        public int TotalScore { get; set; }
        public int TotalAttempts { get; set; }
        public int StudyTimeInSeconds { get; set; }
        public string? ErrorSummary { get; set; } // JSON

        public virtual User? User { get; set; } = null!;
    }
}
