﻿using Newtonsoft.Json;

namespace VietIQ.Elearning.Core
{
    public class JsonStringToObjectConverter<T> : JsonConverter<T> where T : class
    {
        public override bool CanWrite => false;

        public override T? ReadJson(JsonReader reader,
                                    Type objectType,
                                    T? existingValue,
                                    bool hasExistingValue,
                                    JsonSerializer serializer)
        {
            var jsonString = reader.Value?.ToString();
            return string.IsNullOrWhiteSpace(jsonString)
                ? null
                : JsonConvert.DeserializeObject<T>(jsonString);
        }

        public override void WriteJson(JsonWriter writer, T? value, JsonSerializer serializer)
        {
            throw new NotImplementedException("Writing not supported");
        }
    }
}
