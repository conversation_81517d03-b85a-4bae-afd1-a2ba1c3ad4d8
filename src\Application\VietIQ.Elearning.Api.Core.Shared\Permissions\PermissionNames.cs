﻿namespace VietIQ.Elearning.Api.Shared.Permissions
{
    public class PermissionNames
    {
        public abstract class BasePermisssionName<T> where T : class
        {
            public const string View = $"{nameof(T)}.View";
            public const string Add = $"{nameof(T)}.Add";
            public const string Update = $"{nameof(T)}.Update";
            public const string Delete = $"{nameof(T)}.Delete";
        }

        public class Dashboard
        {
            public const string View = "Dashboard.View";
            public const string Add = "Dashboard.Add";
            public const string Update = "Dashboard.Update";
            public const string Delete = "Dashboard.Delete";
        }

        public class OtherUserPermission
        {
            public const string View = "OtherUserPermission.View";
            public const string Update = "OtherUserPermission.Update";
        }

        public class RolePermission
        {
            public const string View = "RolePermission.View";
            public const string Update = "RolePermission.Update";
        }

        public class User
        {
            public const string View = "User.View";
            public const string Add = "User.Add";
            public const string Update = "User.Update";
            public const string Delete = "User.Delete";
            public const string UpdateMaxToken = "User.UpdateMaxToken";
        }

        public class Role
        {
            public const string View = "Role.View";
            public const string Add = "Role.Add";
            public const string Update = "Role.Update";
            public const string Delete = "Role.Delete";
        }

        public class Setting
        {
            public const string View = "Setting.View";
            public const string BasicEdit = "Setting.Basic.Edit";
            public const string SmtpEdit = "Setting.Smtp.Edit";
            public const string ConfirmAccountEdit = "Setting.ConfirmAccount.Edit";
            public const string InformationAccountEdit = "Setting.InformationAccount.Edit";
            public const string AuthenticationEdit = "Setting.Authentication.Edit";
            public const string RateLimitEdit = "Setting.RateLimit.Edit";
            public const string BookConfigEdit = "Setting.BookConfiguration.Edit";
            public const string FCloudEdit = "Setting.FCloud.Edit";
        }

        public class ApiToken
        {
            public const string View = "ApiToken.View";
            public const string Add = "ApiToken.Add";
            public const string Update = "ApiToken.Update";
            public const string Delete = "ApiToken.Delete";
        }

        public class MaxApiTokenThreshold
        {
            public const string View = "MaxApiTokenThreshold.View";
            public const string Update = "MaxApiTokenThreshold.Update";
        }

        public class Session
        {
            public const string View = "Session.View";
            public const string Add = "Session.Add";
            public const string Update = "Session.Update";
            public const string Delete = "Session.Delete";
        }

        public class Notification
        {
            public const string View = "Notification.View";
            public const string Add = "Notification.Add";
            public const string Marked = "Notification.Marked";
            public const string Delete = "Notification.Delete";
        }

        public class Week
        {
            public const string View = "Week.View";
            public const string Add = "Week.Add";
            public const string Update = "Week.Update";
            public const string Delete = "Week.Delete";
        }

        public class Lesson
        {
            public const string View = "Lesson.View";
            public const string Add = "Lesson.Add";
            public const string Update = "Lesson.Update";
            public const string Delete = "Lesson.Delete";
        }

        public class Organization
        {
            public const string View = "Organization.View";
            public const string Add = "Organization.Add";
            public const string Update = "Organization.Update";
            public const string Delete = "Organization.Delete";
        }

        public class Class
        {
            public const string View = "Class.View";
            public const string Add = "Class.Add";
            public const string Update = "Class.Update";
            public const string Delete = "Class.Delete";
        }

        public class Course
        {
            public const string View = "Course.View";
            public const string Add = "Course.Add";
            public const string Update = "Course.Update";
            public const string Delete = "Course.Delete";
        }

        public class Exercise
        {
            public const string View = "Exercise.View";
            public const string Add = "Exercise.Add";
            public const string Update = "Exercise.Update";
            public const string Delete = "Exercise.Delete";
        }

        public class Grade
        {
            public const string View = "Grade.View";
            public const string Add = "Grade.Add";
            public const string Update = "Grade.Update";
            public const string Delete = "Grade.Delete";
        }
    }
}
