﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;
using VietIQ.Elearning.EntityFramework.Entities.Domain;

namespace VietIQ.Elearning.EntityFramework
{
    public class PronunciationResultConfiguration : IEntityTypeConfiguration<PronunciationResult>
    {
        public void Configure(EntityTypeBuilder<PronunciationResult> builder)
        {
            builder.HasOne(pr => pr.UserAnswer)
                .WithOne(ua => ua.PronunciationResult)
                .HasForeignKey<UserAnswer>(ua => ua.PronunciationResultId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(pr => pr.Exercise)
                .WithMany()
                .HasForeignKey(pr => pr.ExerciseId);

            builder.HasOne(pr => pr.User)
                .WithMany()
                .HasForeignKey(pr => pr.UserId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
