﻿using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Api.Shared.Settings;
using VietIQ.Elearning.EntityFramework;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using VietIQ.Elearning.Api.Core.SeedData;

namespace VietIQ.Elearning.Api.Shared.SeedData
{
    public static class WebHostExtensions
    {
        public static void SeedData(this WebApplication app)
        {
            var settingProvider = app.Services.GetService<ISettingProvider>();
            var isDevelopment = app.Environment.IsDevelopment();

            using (var scope = app.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetService<ApplicationDbContext>();
                var permissionDefinitionProvider = scope.ServiceProvider.GetService<IPermissionDefinitionProvider>();
                var permissionContext = scope.ServiceProvider.GetService<IPermissionContext>();

                if (context == null
                    || permissionContext == null
                    || settingProvider == null)
                {
                    Console.WriteLine("Context is NULL");
                    return;
                }

                Console.WriteLine("Start seeding new data");
                new GradeSeeder(context, isDevelopment).SeedData();
                new OrganizationSeeder(context, isDevelopment).SeedData();
                new ClassSeeder(context, isDevelopment).SeedData();

                new RoleSeeder(context, isDevelopment).SeedData();
                new UserSeeder(context, isDevelopment).SeedData();
                new UserRoleSeeder(context, isDevelopment).SeedData();
                new RolePermissionSeeder(context, permissionContext).SeedData();
                new SettingSeeder(context, settingProvider, isDevelopment).SeedData();

                new SubscriptionSeeder(context, isDevelopment).SeedData();
                new SubscriptionUserSeeder(context, isDevelopment).SeedData();
                new SubscriptionClassSeeder(context, isDevelopment).SeedData();
                new CourseSeeder(context, isDevelopment).SeedData();
                new WeekSeeder(context, isDevelopment).SeedData();
                new LessonSeeder(context, isDevelopment).SeedData();
                new ExerciseTypeSeeder(context, isDevelopment).SeedData();
                new TagSeeder(context, isDevelopment).SeedData();
            }
        }
    }
}
