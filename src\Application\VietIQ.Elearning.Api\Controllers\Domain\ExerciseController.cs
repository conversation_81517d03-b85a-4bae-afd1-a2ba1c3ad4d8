﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.ExerciseItems;
using VietIQ.Elearning.Domain.Exercises;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers
{
    [ApiController]
    [Route("api/exercises")]
    [FeatureAuthorize]
    public class ExerciseController : ApiBaseController
    {
        private readonly IAddExerciseUseCase addUseCase;
        private readonly IUpdateExerciseUseCase updateUseCase;
        private readonly IDeleteExerciseUseCase deleteUseCase;
        private readonly IQueryExerciseUseCase queryUseCase;

        public ExerciseController(IAddExerciseUseCase addUseCase,
                                  IUpdateExerciseUseCase updateUseCase,
                                  IDeleteExerciseUseCase deleteUseCase,
                                  IQueryExerciseUseCase queryUseCase)
        {
            this.addUseCase = addUseCase;
            this.updateUseCase = updateUseCase;
            this.deleteUseCase = deleteUseCase;
            this.queryUseCase = queryUseCase;
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Exercise.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var response = await queryUseCase.GetAsync(input);
            return response.ToActionResult();
        }

        [HttpGet("{id}")]
        [FeatureAuthorize(PermissionNames.Exercise.View)]
        public async Task<IActionResult> GetByIdAsync(int id)
        {
            var response = await queryUseCase.GetByIdAsync(id);
            return response.ToActionResult();
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Exercise.Add)]
        public async Task<IActionResult> AddAsync([FromForm] AddExerciseInput input)
        {
            var response = await addUseCase.AddAsync(input);
            return response.ToActionResult();
        }

        [HttpPut("{id}")]
        [FeatureAuthorize(PermissionNames.Exercise.Update)]
        public async Task<IActionResult> UpdateAsync(int id, [FromForm] UpdateExerciseInput input)
        {
            var response = await updateUseCase.UpdateAsync(id, input);
            return response.ToActionResult();
        }

        [HttpPatch("{id}/items")]
        [FeatureAuthorize(PermissionNames.Exercise.Update)]
        public async Task<IActionResult> PatchItemAsync(int id, [FromForm] UpdateExerciseItemInput input)
        {
            var result = await updateUseCase.PatchItemAsync(id, input);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        [FeatureAuthorize(PermissionNames.Exercise.Delete)]
        public async Task<IActionResult> DeleteAsync(int id)
        {
            var response = await deleteUseCase.DeleteAsync(id);
            return response.ToActionResult();
        }
    }
}
