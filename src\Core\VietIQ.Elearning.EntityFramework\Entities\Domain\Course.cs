﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Courses")]
    public class Course : Entity<int>
    {
        public int GradeId { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public string? CourseType { get; set; }

        public virtual Grade Grade { get; set; } = null!;
        public virtual List<Week> Weeks { get; set; } = [];
    }
}
