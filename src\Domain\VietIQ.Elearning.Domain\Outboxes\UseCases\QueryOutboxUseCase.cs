﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Outboxes
{
    public class QueryOutboxUseCase : BaseService, IQueryOutboxUseCase
    {
        public QueryOutboxUseCase(IUnitOfWork unitOfWork, IMapper mapper) : base(unitOfWork, mapper)
        {
        }

        public async Task<IEnumerable<Outbox>> GetIncompleteTasksAsync(string? commandName = null)
        {
            var repository = this.GetGenericRepository<Outbox>();

            if (string.IsNullOrWhiteSpace(commandName))
            {
                // Trả về danh sách toàn bộ chưa hoàn thành
                return await repository.GetAsync(x => !x.IsCompleted);
            }

            // Trả về danh sách chưa hoàn thành theo command
            return await repository.GetAsync(x => !x.IsCompleted && x.CommandName == commandName);
        }
    }
}
