﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Lessons
{
    public class QueryLessonUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IQueryLessonUseCase
    {
        public async Task<IApiResponse> GetAsync(IPaginationInput input)
        {
            var repo = GetGenericRepository<Lesson>();
            var predicate = PredicateBuilder.False<Lesson>();
            if (input.HasSearchTerm)
            {
                predicate = predicate.Or(x => EF.Functions.Like(x.Title, input.SearchTermPattern));
            }

            var result = await repo.AsQueryable()
                                   .WhereIf(input.HasSearchTerm, predicate)
                                   .Include(x => x.Week)
                                   .ToPaginationAsync(input);
            return MapPagination<Lesson, LessonModel>(result).ToApiResponse();
        }

        public async Task<IApiResponse> GetByIdAsync(int id)
        {
            var repo = GetGenericRepository<Lesson, int>();
            var entity = await repo.FindAsync(x => x.Id == id);
            if (entity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<LessonModel>(entity));
        }
    }
}
