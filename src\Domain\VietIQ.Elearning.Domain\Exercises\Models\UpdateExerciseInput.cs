﻿using VietIQ.Elearning.Domain.ExerciseItems;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Exercises
{
    public class UpdateExerciseInput
    {
        public int LessonId { get; set; }
        public int ClassId { get; set; }
        public int WeekId { get; set; }
        public string? Title { get; set; }
        public ExerciseTypeEnum TypeId { get; set; }
        public string? ExerciseType { get; set; }
        public string? QuestionData { get; set; }
        public string? CorrectAnswers { get; set; }
        public string? Metadata { get; set; }

        public List<AddExerciseItemInput> Items { get; set; } = [];
    }

}
