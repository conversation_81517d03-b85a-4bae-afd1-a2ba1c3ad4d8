﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace VietIQ.Elearning.Api.Shared
{
    public static class Cors
    {
        private const string allowSpecificOrigins = "ApplicationSpecificOrigins";

        public static void AddCustomCors(this IServiceCollection services)
        {
            services.AddCors(options =>
            {
                options.AddPolicy(name: allowSpecificOrigins,
                                policy =>
                                {
                                    policy
                                    .WithOrigins("http://localhost:7001",
                                                "http://localhost:7002",
                                                "http://localhost:3000",
                                                "http://localhost:3001",
                                                "http://localhost:5173")
                                    .AllowAnyHeader()
                                    .AllowAnyMethod()
                                    .AllowCredentials();
                                });
            });
        }

        public static void UseCustomCors(this IApplicationBuilder builder)
        {
            builder.UseCors(allowSpecificOrigins);
        }
    }
}