﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class OrganizationConfiguration : IEntityTypeConfiguration<Organization>
    {
        public void Configure(EntityTypeBuilder<Organization> builder)
        {
            builder.HasKey(i => i.Id);

            builder.Property(i => i.Name)
                   .HasMaxLength(255)
                   .IsRequired();

            builder.HasIndex(i => i.Name)
                   .HasDatabaseName("IX_Organization_Name");

            builder.HasMany(i => i.Classes)
                   .WithOne(c => c.Organization)
                   .HasForeignKey(c => c.OrganizationId);
        }
    }
}
