﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using VietIQ.Elearning.EntityFramework.Entities;
using VietIQ.Elearning.EntityFramework.Entities.Domain;

namespace VietIQ.Elearning.EntityFramework
{
    public class ApplicationDbContext : IdentityDbContext<User, Role, int>
    {
        private readonly string? loggedUserId;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options,
                                    IHttpContextAccessor httpContextAccessor) : base(options)
        {
            this.loggedUserId = httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);
        }

        #region DBSet

        public DbSet<Permission>? Permissions { get; }
        public DbSet<Setting>? Settings { get; }
        public DbSet<ApiToken>? ApiTokens { get; }
        public DbSet<OperationLog>? OperationLogs { get; }
        public DbSet<UserNotification>? UserNotifications { get; }
        public DbSet<Notification>? Notifications { get; }

        public DbSet<Organization>? Organizations { get; }
        public DbSet<Grade>? Grades { get; }
        public DbSet<Class>? Classes { get; }
        public DbSet<Subscription>? Subscriptions { get; }
        public DbSet<SubscriptionUser>? SubscriptionUsers { get; }
        public DbSet<SubscriptionClass>? SubscriptionClasses { get; }
        public DbSet<Course>? Courses { get; }
        public DbSet<Week>? Weeks { get; }
        public DbSet<Lesson>? Lessons { get; }
        public DbSet<Exercise>? Exercises { get; }
        public DbSet<ExerciseItem>? ExerciseItems { get; }
        public DbSet<ExerciseAnswer>? ExerciseAnswers { get; }
        public DbSet<MediaFile>? MediaFiles { get; }
        public DbSet<ExerciseType>? ExerciseTypes { get; }
        public DbSet<ExerciseTag>? ExerciseTags { get; }
        public DbSet<Tag>? Tags { get; }
        public DbSet<UserProgress>? Progresses { get; }
        public DbSet<UserAnswer>? UserAnswers { get; }
        public DbSet<PronunciationResult>? PronunciationResults { get; }
        public DbSet<WeeklyStat>? WeeklyStats { get; }
        public DbSet<PronunciationError>? PronunciationErrors { get; }
        public DbSet<AIRecommendation>? AIRecommendations { get; }
        public DbSet<TestEntity>? TestEntities { get; }
        public DbSet<Outbox>? Outboxes { get; }
        public DbSet<OutboxHistory>? OutboxHistories { get; }

        #endregion

        protected override void OnModelCreating(ModelBuilder builder)
        {
            AddQueryFilters(builder);
            builder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
            base.OnModelCreating(builder);
            foreach (var entityType in builder.Model.GetEntityTypes())
            {
                var tableName = entityType.GetTableName();
                if (tableName != null && tableName.StartsWith("AspNet"))
                {
                    entityType.SetTableName("Core_" + tableName[6..]);
                }
            }

        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            BeforeSaveChange();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void BeforeSaveChange()
        {
            foreach (var entity in ChangeTracker.Entries().Where(x => x.Entity is IAuditEntity && x.State == EntityState.Added))
            {
                if (typeof(IAuditEntity).IsAssignableFrom(entity.Metadata.ClrType))
                {
                    ((IAuditEntity)entity.Entity).CreatedTime = DateTimeOffset.UtcNow;
                    if (!string.IsNullOrEmpty(loggedUserId))
                    {
                        ((IAuditEntity)entity.Entity).CreatedBy = loggedUserId;
                    }
                }
            }

            foreach (var entity in ChangeTracker.Entries().Where(x => x.Entity is IAuditEntity && x.State == EntityState.Modified))
            {
                if (typeof(IAuditEntity).IsAssignableFrom(entity.Metadata.ClrType))
                {
                    ((IAuditEntity)entity.Entity).UpdatedTime = DateTimeOffset.UtcNow;
                    ((IAuditEntity)entity.Entity).UpdatedBy = loggedUserId;
                }
            }

            foreach (var entity in ChangeTracker.Entries().Where(x => x.Entity is ISoftDelete && x.State == EntityState.Deleted))
            {
                if (typeof(ISoftDelete).IsAssignableFrom(entity.Metadata.ClrType))
                {
                    entity.State = EntityState.Modified;
                    var softDeleteEntity = (ISoftDelete)entity.Entity;
                    softDeleteEntity.DeletedTime = DateTimeOffset.UtcNow;
                    softDeleteEntity.DeletedBy = loggedUserId;
                    softDeleteEntity.IsDeleted = true;
                }
            }
        }

        private static void AddQueryFilters(ModelBuilder builder)
        {
            foreach (var entityType in builder.Model.GetEntityTypes())
            {
                // Other automated configurations left out
                if (typeof(ISoftDelete).IsAssignableFrom(entityType.ClrType))
                {
                    entityType.AddSoftDeleteQueryFilter();
                }
            }
        }
    }
}
