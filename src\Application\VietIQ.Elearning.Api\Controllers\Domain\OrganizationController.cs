﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.Organizations;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers.Domain
{
    [ApiController]
    [Route("api/organizations")]
    [FeatureAuthorize]
    public class OrganizationController : ApiBaseController
    {
        private readonly IAddOrganizationUseCase addOrganizationUseCase;
        private readonly IQueryOrganizationUseCase queryOrganizationUseCase;
        private readonly IUpdateOrganizationUseCase updateOrganizationUseCase;
        private readonly IDeleteOrganizationUseCase deleteOrganizationUseCase;

        public OrganizationController(IAddOrganizationUseCase addOrganizationUseCase,
                                      IQueryOrganizationUseCase queryOrganizationUseCase,
                                      IUpdateOrganizationUseCase updateOrganizationUseCase,
                                      IDeleteOrganizationUseCase deleteOrganizationUseCase)
        {
            this.addOrganizationUseCase = addOrganizationUseCase;
            this.queryOrganizationUseCase = queryOrganizationUseCase;
            this.updateOrganizationUseCase = updateOrganizationUseCase;
            this.deleteOrganizationUseCase = deleteOrganizationUseCase;
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Organization.Add)]
        public async Task<IActionResult> AddAsync([FromBody] AddOrganizationInput input)
        {
            var result = await addOrganizationUseCase.AddAsync(input);
            return result.ToActionResult();
        }

        [HttpPut("{organizationId}")]
        [FeatureAuthorize(PermissionNames.Organization.Update)]
        public async Task<IActionResult> UpdateAsync(int organizationId, [FromBody] UpdateOrganizationInput input)
        {
            var result = await updateOrganizationUseCase.UpdateAsync(organizationId, input);
            return result.ToActionResult();
        }

        [HttpDelete("{organizationId}")]
        [FeatureAuthorize(PermissionNames.Organization.Delete)]
        public async Task<IActionResult> DeleteAsync(int organizationId)
        {
            var result = await deleteOrganizationUseCase.DeleteAsync(organizationId);
            return result.ToActionResult();
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Organization.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var result = await queryOrganizationUseCase.GetAsync(input);
            return result.ToActionResult();
        }

        [HttpGet("all")]
        [FeatureAuthorize(PermissionNames.Organization.View)]
        public async Task<IActionResult> GetAllAsync()
        {
            var result = await queryOrganizationUseCase.GetAllAsync();
            return result.ToActionResult();
        }

        [HttpGet("{organizationId}")]
        [FeatureAuthorize(PermissionNames.Organization.View)]
        public async Task<IActionResult> GetByOrganizationIdAsync(int organizationId)
        {
            var result = await queryOrganizationUseCase.GetOrganizationByIdAsync(organizationId);
            return result.ToActionResult();
        }

        [HttpGet("get-classes/{organizationId}")]
        [FeatureAuthorize(PermissionNames.Organization.View)]
        public async Task<IActionResult> GetClassesByOrganizationIdAsync(int organizationId)
        {
            var result = await queryOrganizationUseCase.GetClassesByOrganizationIdAsync(organizationId);
            return result.ToActionResult();
        }

        [HttpGet("get-users/{organizationId}")]
        [FeatureAuthorize(PermissionNames.Organization.View)]
        public async Task<IActionResult> GetUsersByOrganizationIdAsync(int organizationId)
        {
            var result = await queryOrganizationUseCase.GetUsersByOrganizationIdAsync(organizationId);
            return result.ToActionResult();
        }
    }
}
