﻿namespace VietIQ.Elearning.Core.Constants
{
    public class SettingKey
    {
        public class ConnectionString
        {
            public static string Redis = "RedisConnectionString";
        }

        public class RateLimit
        {
            public static string UserDefaultPoint = $"{nameof(RateLimit)}:{nameof(UserDefaultPoint)}";
            public static string DefaultMinusPoint = $"{nameof(RateLimit)}:{nameof(DefaultMinusPoint)}";
        }

        public class ConfirmAccount
        {
            public static string Enable = $"{nameof(ConfirmAccount)}:{nameof(Enable)}";
            public static string EmailTile = $"{nameof(ConfirmAccount)}:{nameof(EmailTile)}";
            public static string EmailTemplate = $"{nameof(ConfirmAccount)}:{nameof(EmailTemplate)}";
            public static string UrlFrontend = $"{nameof(ConfirmAccount)}:{nameof(UrlFrontend)}";
        }

        public class InformationAccount
        {
            public static string Enable = $"{nameof(InformationAccount)}:{nameof(Enable)}";
            public static string EmailTile = $"{nameof(InformationAccount)}:{nameof(EmailTile)}";
            public static string EmailTemplate = $"{nameof(InformationAccount)}:{nameof(EmailTemplate)}";
        }

        public class FCloud
        {
            public static string SecretId = $"{nameof(FCloud)}:{nameof(SecretId)}";
            public static string SecretKey = $"{nameof(FCloud)}:{nameof(SecretKey)}";
            public static string AppId = $"{nameof(FCloud)}:{nameof(AppId)}";
        }

        public class Upload
        {
            private const string Prefix = nameof(Upload);

            public static readonly string BasePath = $"{Prefix}:BasePath";

            public static readonly string ImagePath = $"{Prefix}:ImagePath";
            public static readonly string DocumentPath = $"{Prefix}:DocumentPath";
            public static readonly string TempPath = $"{Prefix}:TempPath";
            public static readonly string CategoryPath = $"{Prefix}:CategoryPath";
            public static readonly string ProductPath = $"{Prefix}:ProductPath";
            public static readonly string EditorPath = $"{Prefix}:EditorPath";

            public static readonly string MaxSize = $"{Prefix}:MaxSize";

            public class AllowedType
            {
                private const string AllowedTypePrefix = $"{Prefix}:AllowedType";

                public static readonly string Image = $"{AllowedTypePrefix}:Images";
                public static readonly string Document = $"{AllowedTypePrefix}:Files";
            }

            public class ImageContraints
            {
                private const string ImageConstraintsPrefix = $"{Prefix}:ImageConstraints";

                public static readonly string MaxWidth = $"{ImageConstraintsPrefix}:MaxWidth";
                public static readonly string MaxHeight = $"{ImageConstraintsPrefix}:MaxHeight";
            }

            public class ThumbSize
            {
                private const string ThumbSizePrefix = $"{Prefix}:ThumbSize";

                public static class Small
                {
                    private const string SmallPrefix = $"{ThumbSizePrefix}:Small";

                    public static readonly string Width = $"{SmallPrefix}:Width";
                    public static readonly string Height = $"{SmallPrefix}:Height";
                }

                public static class Medium
                {
                    private const string MediumPrefix = $"{ThumbSizePrefix}:Medium";

                    public static readonly string Width = $"{MediumPrefix}:Width";
                    public static readonly string Height = $"{MediumPrefix}:Height";
                }
            }
        }
    }
}
