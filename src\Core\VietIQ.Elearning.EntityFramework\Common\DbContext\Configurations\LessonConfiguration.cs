﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class LessonConfiguration : IEntityTypeConfiguration<Lesson>
    {
        public void Configure(EntityTypeBuilder<Lesson> builder)
        {
            builder.HasKey(l => l.Id);

            builder.HasIndex(l => l.WeekId)
                   .HasDatabaseName("IX_Lesson_WeekId");

            builder.HasMany(l => l.Exercises)
                   .WithOne(e => e.Lesson)
                   .HasForeignKey(e => e.LessonId);
        }
    }
}
