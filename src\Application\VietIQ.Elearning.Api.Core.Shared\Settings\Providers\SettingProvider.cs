﻿using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Core.Constants;
using Microsoft.AspNetCore.Http;

namespace VietIQ.Elearning.Api.Shared.Settings
{
    public class SettingProvider(
        ISettingContext settingContext,
        IHttpContextAccessor httpContextAccessor) : BaseSettingProvider(settingContext, httpContextAccessor)
    {
        protected override void Define(ISettingContext context)
        {
            AddBasic(context);
            AddSmtp(context);
            AddConfirmAccount(context);
            AddInformationAccount(context);
            AddAuthentication(context);
            AddRateLimitOptions(context);
            AddFCloudOptions(context);
        }

        private static void AddBasic(ISettingContext context)
        {
            var baseGroup = context.AddGroup(SettingName.Basic.GroupName)
                                   .SetTitle(MessageDefinition.Setting.Basic.Root1)
                                   .AddPermission(PermissionNames.Setting.BasicEdit);

            baseGroup.AddItem(new SettingItem()
            {
                Name = "Setting1",
                Value = "Value string 1",
                Title = MessageDefinition.Setting.Basic.String,
                Schema = SettingSchemaBuilder.String(),
                Action = ActionType.Edit,
            });

            baseGroup.AddItem(new SettingItem()
            {
                Name = "Setting2",
                Value = true,
                Title = MessageDefinition.Setting.Basic.Boolean,
                Schema = SettingSchemaBuilder.Boolean(),
                Action = ActionType.Edit,
            });

            baseGroup.AddItem(new SettingItem()
            {
                Name = "Setting3",
                Value = 3,
                Title = MessageDefinition.Setting.Basic.Integer,
                Schema = SettingSchemaBuilder.Integer(),
                Action = ActionType.View,
            });

            baseGroup.AddItem(new SettingItem()
            {
                Name = "Setting4",
                Value = "item1",
                Title = MessageDefinition.Setting.Basic.Collection,
                Schema = SettingSchemaBuilder.Collection("item1", "item2", "item3"),
                Action = ActionType.View,
            });

            var baseGroup2 = context.AddGroup("Basic2")
                                    .SetTitle(MessageDefinition.Setting.Basic.Root1)
                                    .AddPermission(PermissionNames.Setting.BasicEdit);

            baseGroup2.AddItem(new SettingItem()
            {
                Name = "Item1",
                Value = "Value x1",
                Title = MessageDefinition.Setting.Basic.String,
                Schema = SettingSchemaBuilder.String(),
                Action = ActionType.View,
            });
        }

        private static void AddSmtp(ISettingContext context)
        {
            var smtp = context.AddGroup(SettingName.Smtp.GroupName)
                               .SetTitle(MessageDefinition.Setting.Smtp.Root)
                               .AddPermission(PermissionNames.Setting.SmtpEdit);

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.Host,
                Value = null,
                Title = MessageDefinition.Setting.Smtp.Host,
                Schema = SettingSchemaBuilder.String(true),
                Action = ActionType.Edit,
            });

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.Port,
                Value = null,
                Title = MessageDefinition.Setting.Smtp.Port,
                Schema = SettingSchemaBuilder.Integer(100, 999)
            });

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.Name,
                Value = "Fresher Dev",
                Title = MessageDefinition.Setting.Smtp.Name,
                Schema = SettingSchemaBuilder.String(true)
            });

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.From,
                Value = null,
                Title = MessageDefinition.Setting.Smtp.From,
                Schema = SettingSchemaBuilder.String(true, AppRegex.EmailRegex)
            });

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.Username,
                Value = null,
                Title = MessageDefinition.Setting.Smtp.Username,
                Schema = SettingSchemaBuilder.String(true, AppRegex.EmailRegex)
            });

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.Password,
                Value = null,
                Title = MessageDefinition.Setting.Smtp.Password,
                Schema = SettingSchemaBuilder.Password(true, AppRegex.Least6Characters)
            });

            smtp.AddItem(new SettingItem()
            {
                Name = SettingName.Smtp.SecureSocket,
                Value = "None",
                Title = MessageDefinition.Setting.Smtp.SecureSocket,
                Schema = SettingSchemaBuilder.Collection("None", "Ssl", "Startssl")
            });
        }

        private static void AddConfirmAccount(ISettingContext context)
        {
            var group = context.AddGroup(SettingName.ConfirmAccount.GroupName)
                               .SetTitle(MessageDefinition.Setting.ConfirmAccount.Root)
                               .AddPermission(PermissionNames.Setting.ConfirmAccountEdit);

            group.AddItem(new SettingItem()
            {
                Name = SettingName.ConfirmAccount.Enable,
                Value = false,
                Title = MessageDefinition.Setting.ConfirmAccount.Enable,
                Schema = SettingSchemaBuilder.Boolean(),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.ConfirmAccount.FrontendUrl,
                Value = null,
                Title = MessageDefinition.Setting.ConfirmAccount.FrontendUrl,
                Schema = SettingSchemaBuilder.String(true, AppRegex.UrlRegex),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.ConfirmAccount.BackendUrl,
                Value = null,
                Title = MessageDefinition.Setting.ConfirmAccount.BackendUrl,
                Schema = SettingSchemaBuilder.String(true, AppRegex.UrlRegex),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.ConfirmAccount.EmailTitle,
                Value = "This is title of email",
                Title = MessageDefinition.Setting.ConfirmAccount.EmailTitle,
                Schema = SettingSchemaBuilder.String()
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.ConfirmAccount.EmailTemplate,
                Value = "asset\\templates\\confirm-email.html",
                Title = MessageDefinition.Setting.ConfirmAccount.EmailTemplate,
                Schema = SettingSchemaBuilder.String()
            });
        }

        private static void AddInformationAccount(ISettingContext context)
        {
            var group = context.AddGroup(SettingName.InformationAccount.GroupName)
                               .SetTitle(MessageDefinition.Setting.InformationAccount.Root)
                               .AddPermission(PermissionNames.Setting.InformationAccountEdit);

            group.AddItem(new SettingItem()
            {
                Name = SettingName.InformationAccount.Enable,
                Value = false,
                Title = MessageDefinition.Setting.InformationAccount.Enable,
                Schema = SettingSchemaBuilder.Boolean(),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.InformationAccount.FrontendUrl,
                Value = null,
                Title = MessageDefinition.Setting.InformationAccount.FrontendUrl,
                Schema = SettingSchemaBuilder.String(true, AppRegex.UrlRegex),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.InformationAccount.BackendUrl,
                Value = null,
                Title = MessageDefinition.Setting.InformationAccount.BackendUrl,
                Schema = SettingSchemaBuilder.String(true, AppRegex.UrlRegex),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.InformationAccount.EmailTitle,
                Value = "This is title of email",
                Title = MessageDefinition.Setting.InformationAccount.EmailTitle,
                Schema = SettingSchemaBuilder.String()
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.InformationAccount.EmailTemplate,
                Value = "asset\\templates\\information-email.html",
                Title = MessageDefinition.Setting.InformationAccount.EmailTemplate,
                Schema = SettingSchemaBuilder.String()
            });
        }

        private static void AddAuthentication(ISettingContext context)
        {
            var group = context.AddGroup(SettingName.Authentication.GroupName)
                               .SetTitle(MessageDefinition.Setting.Authentiaction.Root)
                               .AddPermission(PermissionNames.Setting.AuthenticationEdit);

            group.AddItem(new SettingItem()
            {
                Name = SettingName.Authentication.TokenExpired,
                Value = 30,
                Title = MessageDefinition.Setting.Authentiaction.TokenExpired,
                Schema = SettingSchemaBuilder.Integer(1, 120),
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.Authentication.RefreshTokenExpired,
                Value = 24 * 60,
                Title = MessageDefinition.Setting.Authentiaction.RefreshTokenExpired,
                Schema = SettingSchemaBuilder.Integer(1, 90 * 24 * 60),
            });
        }

        private static void AddRateLimitOptions(ISettingContext context)
        {
            var group = context.AddGroup(SettingName.RateLimit.GroupName)
                               .SetTitle(MessageDefinition.Setting.RateLimit.Root)
                               .AddPermission(PermissionNames.Setting.RateLimitEdit);

            group.AddItem(new SettingItem()
            {
                Name = SettingName.RateLimit.UserDefaultPoint,
                Value = 100,
                Title = MessageDefinition.Setting.RateLimit.UserDefaultPoint,
                Schema = SettingSchemaBuilder.Integer(10, 1000)
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.RateLimit.DefaultMinusPoint,
                Value = 5,
                Title = MessageDefinition.Setting.RateLimit.DefaultMinusPoint,
                Schema = SettingSchemaBuilder.Integer(1, 1000)
            });
        }

        private static void AddFCloudOptions(ISettingContext context)
        {
            var group = context.AddGroup(SettingName.FCloud.GroupName)
                               .SetTitle(MessageDefinition.Setting.FCloud.Root)
                               .AddPermission(PermissionNames.Setting.FCloudEdit);

            group.AddItem(new SettingItem()
            {
                Name = SettingName.FCloud.SecretKey,
                Value = "85dade3d2d70813b9adf76783fa3328a",
                Title = MessageDefinition.Setting.FCloud.SecretKey,
                Schema = SettingSchemaBuilder.String()
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.FCloud.SecretId,
                Value = 100050,
                Title = MessageDefinition.Setting.FCloud.SecretId,
                Schema = SettingSchemaBuilder.Integer()
            });

            group.AddItem(new SettingItem()
            {
                Name = SettingName.FCloud.AppId,
                Value = 100050,
                Title = MessageDefinition.Setting.FCloud.AppId,
                Schema = SettingSchemaBuilder.Integer()
            });
        }
    }
}