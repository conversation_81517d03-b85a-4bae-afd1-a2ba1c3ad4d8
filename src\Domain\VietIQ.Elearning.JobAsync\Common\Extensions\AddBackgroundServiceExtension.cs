﻿using Microsoft.Extensions.DependencyInjection;
using VietIQ.Elearning.JobAsync.Queues;

namespace VietIQ.Elearning.JobAsync
{
    public static class AddBackgroundServiceExtensions
    {
        public static void AddBackgroundService(this IServiceCollection services)
        {
            services.AddSingleton<IBackgroundTaskQueue, FileNameBackgroundTaskQueue>();
            //services.AddHostedService<AddScoreProcessor>();
        }
    }
}
