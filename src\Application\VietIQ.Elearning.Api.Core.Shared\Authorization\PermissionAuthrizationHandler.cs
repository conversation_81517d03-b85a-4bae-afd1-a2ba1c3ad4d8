﻿using VietIQ.Elearning.Core.Constants;
using Microsoft.AspNetCore.Authorization;

namespace VietIQ.Elearning.Api.Shared.Authorization
{
    public class PermissionAuthrizationHandler : AuthorizationHandler<PermissionRequirement>
    {
        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            PermissionRequirement requirement)
        {
            if (context.User == null)
            {
                return Task.CompletedTask;
            }

            // Nếu requirment được đánh là chỉ cần login
            if (context.User.Identity != null
                && context.User.Identity.IsAuthenticated
                && requirement.PermissionName == FeatureAuthorizeConstants.LoginOnly)
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            // Lấy ra permission claim
            var claimPermission = context.User.Claims.FirstOrDefault(x => x.Type == AppClaimTypes.Permission);
            if (claimPermission == null)
            {
                return Task.CompletedTask;
            }

            // Cắt danh sách các permissions mà User có
            var userClaimPermissions = claimPermission.Value.Split('|');

            // Nếu user có chứa permission yêu cầu
            // thì đồng nghĩa với việc user đã đáp ứng requirement này
            // Lưu ý: Trong 1 Controller sẽ có thể chứa nhiều requirement.
            // Cần phải đáp ứng tất cả các requirement thì mới được coi là có quyền truy cập
            if (userClaimPermissions.Contains(requirement.PermissionName))
            {
                context.Succeed(requirement);
            }

            return Task.CompletedTask;
        }
    }
}
