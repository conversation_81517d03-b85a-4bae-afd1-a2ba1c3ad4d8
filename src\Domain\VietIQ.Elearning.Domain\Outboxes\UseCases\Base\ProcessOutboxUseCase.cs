﻿using AutoMapper;
using System.Text.Json;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Outboxes
{
    public abstract class ProcessOutboxUseCase<TInput> : BaseService, IProcessOutboxUseCase
      where TInput : class
    {
        protected ProcessOutboxUseCase(IUnitOfWork unitOfWork, IMapper mapper)
            : base(unitOfWork, mapper)
        {
        }

        protected abstract Task<IApiResponse> LocalExecuteAsync(TInput input, Outbox outbox);

        protected virtual Task ExecuteSuccedAsync(TInput input, Outbox outbox)
        {
            return Task.CompletedTask;
        }

        protected virtual Task ExecuteFailedAsync(Outbox outbox)
        {
            return Task.CompletedTask;
        }

        public async Task<IApiResponse> ExecuteAsync(Outbox outbox)
        {
            using var transaction = CreateTransaction();
            try
            {
                var outboxRepository = this.GetGenericRepository<Outbox>();

                // Parse chuỗi JSON thành object
                var input = JsonSerializer.Deserialize<TInput>(outbox.Data)!;

                var localExecuteResponse = await LocalExecuteAsync(input, outbox);
                if (!localExecuteResponse.IsSuccess)
                {
                    return localExecuteResponse;
                }

                // Đánh dấu trạng thái outbox thành công
                outbox.IsCompleted = true;
                outboxRepository.Update(outbox);
                await this.CommitAsync();

                transaction.Commit();

                await ExecuteSuccedAsync(input, outbox);

                return ApiResponse.Ok(MessageDefinition.Succeeded);
            }
            catch
            {
                transaction.Rollback();
                await ExecuteFailedAsync(outbox);
                throw;
            }
        }
    }
}
