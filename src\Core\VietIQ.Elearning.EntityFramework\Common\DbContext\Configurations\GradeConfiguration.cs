﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class GradeConfiguration : IEntityTypeConfiguration<Grade>
    {
        public void Configure(EntityTypeBuilder<Grade> builder)
        {
            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.HasIndex(c => c.Name)
                   .HasDatabaseName("IX_Grade_Name");

            builder.HasMany(i => i.Courses)
                   .WithOne(u => u.Grade)
                   .HasForeignKey(u => u.GradeId);

            builder.HasMany(i => i.Classes)
                   .WithOne(u => u.Grade)
                   .HasForeignKey(u => u.GradeId);
        }
    }
}
