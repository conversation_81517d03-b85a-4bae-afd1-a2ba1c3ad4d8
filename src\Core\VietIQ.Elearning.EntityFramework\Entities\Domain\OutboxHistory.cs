﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework
{
    [Table("OutboxHistories")]
    public class OutboxHistory : IEntity<Guid>
    {
        [Key]
        public Guid Id { get; set; }

        public Guid OutboxId { get; set; }

        public bool IsSuccess { get; set; }

        [MaxLength(1024)]
        public string? Message { get; set; }

        public DateTime TimeExecuted { get; set; }

        public virtual Outbox? Outbox { get; set; }
    }
}
