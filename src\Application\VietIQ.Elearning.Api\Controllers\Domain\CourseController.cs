﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.Courses;
using VietIQ.Elearning.Domain.Weeks;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers
{
    [ApiController]
    [Route("api/courses")]
    [FeatureAuthorize]
    public class CourseController : ApiBaseController
    {
        private readonly IAddCourseUseCase addUseCase;
        private readonly IUpdateCourseUseCase updateUseCase;
        private readonly IDeleteCourseUseCase deleteUseCase;
        private readonly IQueryCourseUseCase queryUseCase;
        private readonly IQueryWeeksByCourseIdUseCase queryWeeksByCourseIdUseCase;

        public CourseController(IAddCourseUseCase addUseCase,
                                IUpdateCourseUseCase updateUseCase,
                                IDeleteCourseUseCase deleteUseCase,
                                IQueryCourseUseCase queryUseCase,
                                IQueryWeeksByCourseIdUseCase queryWeeksByCourseIdUseCase)
        {
            this.addUseCase = addUseCase;
            this.updateUseCase = updateUseCase;
            this.deleteUseCase = deleteUseCase;
            this.queryUseCase = queryUseCase;
            this.queryWeeksByCourseIdUseCase = queryWeeksByCourseIdUseCase;
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Course.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var response = await queryUseCase.GetAsync(input);
            return response.ToActionResult();
        }

        [HttpGet("{id}")]
        [FeatureAuthorize(PermissionNames.Course.View)]
        public async Task<IActionResult> GetByIdAsync(int id)
        {
            var response = await queryUseCase.GetByIdAsync(id);
            return response.ToActionResult();
        }

        [HttpGet("{courseId}/weeks")]
        [FeatureAuthorize(PermissionNames.Course.View)]
        public async Task<IActionResult> GetWeeksByCourseIdAsync(int courseId)
        {
            var response = await queryWeeksByCourseIdUseCase.ExecuteAsync(courseId);
            return response.ToActionResult();
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Course.Add)]
        public async Task<IActionResult> AddAsync([FromBody] AddCourseInput input)
        {
            var response = await addUseCase.AddAsync(input);
            return response.ToActionResult();
        }

        [HttpPut("{id}")]
        [FeatureAuthorize(PermissionNames.Course.Update)]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateCourseInput input)
        {
            var response = await updateUseCase.UpdateAsync(id, input);
            return response.ToActionResult();
        }

        [HttpDelete("{id}")]
        [FeatureAuthorize(PermissionNames.Course.Delete)]
        public async Task<IActionResult> Delete(int id)
        {
            var response = await deleteUseCase.DeleteAsync(id);
            return response.ToActionResult();
        }
    }
}
