﻿using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Core.SeedData
{
    class LessonSeeder : BaseDataSeeder
    {
        public LessonSeeder(ApplicationDbContext context, bool isDevelopment) : base(context, isDevelopment)
        {
        }
        public void SeedData()
        {
            var dbSet = this.Context.Set<Lesson>();
            if (dbSet != null && !dbSet.Any() && IsDevelopment)
            {
                dbSet.AddRange(
                    new Lesson { Id = 1, Title = "Greetings", Description = "Chào hỏi cơ bản", Order = 1, WeekId = 1 },
                    new Lesson { Id = 2, Title = "Family", Description = "Từ vựng về gia đình", Order = 2, WeekId = 1 },
                    new Lesson { Id = 3, Title = "Numbers", Description = "Đếm số từ 1 đến 20", Order = 3, WeekId = 1 },
                    new Lesson { Id = 4, Title = "Colors", Description = "Tên các màu sắc", Order = 1, WeekId = 2 },
                    new Lesson { Id = 5, Title = "Fruits", Description = "Tên trái cây phổ biến", Order = 2, WeekId = 2 },
                    new Lesson { Id = 6, Title = "Animals", Description = "Tên các con vật", Order = 3, WeekId = 2 },
                    new Lesson { Id = 7, Title = "Body Parts", Description = "Các bộ phận cơ thể", Order = 4, WeekId = 2 },
                    new Lesson { Id = 8, Title = "Weather", Description = "Thời tiết", Order = 5, WeekId = 2 },
                    new Lesson { Id = 9, Title = "Emotions", Description = "Cảm xúc", Order = 6, WeekId = 2 },
                    new Lesson { Id = 10, Title = "Daily Activities", Description = "Các hoạt động thường ngày", Order = 7, WeekId = 2 }
                );

                this.Context.SaveChanges();
            }
        }
    }
}
