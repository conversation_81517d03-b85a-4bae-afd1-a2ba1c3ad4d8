﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities.Domain
{
    [Table("PronunciationResults")]
    public class PronunciationResult : AuditEntity<int>
    {
        [Required]
        public string RequestId { get; set; } = Guid.NewGuid().ToString();

        public int? ExerciseId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Range(0, 100, ErrorMessage = "Score must be between 0 and 100")]
        public int? Score { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "Feedback cannot exceed {1} characters")]
        public string? Feedback { get; set; }

        [StringLength(EntityLength.Normal, ErrorMessage = "Status cannot exceed {1} characters")]
        public string Status { get; set; } = "started";

        public int? ErrorCode { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "Error message cannot exceed {1} characters")]
        public string? ErrorMessage { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "Audio file URL cannot exceed {1} characters")]
        public string? AudioFileUrl { get; set; }

        public double? ProcessingTime { get; set; }

        public double? WordsPerMin { get; set; }

        public double? SpeakingDuration { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "User phoneme cannot exceed {1} characters")]
        public string? UserPhoneme { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "Words score detail cannot exceed {1} characters")]
        public string? WordsScoreDetail { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "Phoneme score statistics cannot exceed {1} characters")]
        public string? PhonemeScoreStatistics { get; set; }

        [StringLength(EntityLength.Long, ErrorMessage = "Stress response cannot exceed {1} characters")]
        public string? StressResponse { get; set; }

        public virtual UserAnswer? UserAnswer { get; set; }

        public virtual Exercise Exercise { get; set; } = null!;

        public virtual User User { get; set; } = null!;
    }
}
