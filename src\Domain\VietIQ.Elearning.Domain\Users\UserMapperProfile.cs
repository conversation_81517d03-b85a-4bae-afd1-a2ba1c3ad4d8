﻿using AutoMapper;
using VietIQ.Elearning.Domain.Classes;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Users
{
    public class UserMapperProfile : Profile
    {
        public UserMapperProfile()
        {
            CreateMap<User, ExtendedUserModel>()
                .ForMember(dest => dest.Class, opt =>
                {
                    opt.MapFrom(src => src.Class == null ? null : new ClassModel
                    {
                        Id = src.Class.Id,
                        Code = src.Class.Code,
                        Name = src.Class.Name,
                    });
                });
        }
    }
}
