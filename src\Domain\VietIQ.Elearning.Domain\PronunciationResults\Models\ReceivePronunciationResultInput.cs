﻿namespace VietIQ.Elearning.Domain.PronunciationResults
{
    public class ReceivePronunciationResultInput
    {
        public string ApiKey { get; set; } = string.Empty;

        public string AppId { get; set; } = string.Empty;

        public string SecretKey { get; set; } = string.Empty;

        public string Signature { get;set; } = string.Empty;

        public dynamic Input { get; set; } = null!;
    }
}
