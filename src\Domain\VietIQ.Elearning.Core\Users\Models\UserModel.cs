﻿namespace VietIQ.Elearning.Core.Users
{
    public class UserModel
    {
        public int Id { get; set; }

        public string UserName { get; set; } = null!;

        public string? Email { get; set; }

        public string? PhoneNumber { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Address { get; set; }

        public string? Token { get; set; }

        public double Total { get; set; }

        public int MaxTokenCount { get; set; }

        public double Balance { get; set; }

        public bool IsEnabled { get; set; }

        public string? ImageUrl { get; set; }

        public int? OrganizationId { get; set; }

        public int? ClassId { get; set; }

        public DateTimeOffset CreatedTime { get; set; }

        public DateTimeOffset? UpdatedTime { get; set; }

        public DateTimeOffset? LastLoginTime { get; set; }
    }
}
