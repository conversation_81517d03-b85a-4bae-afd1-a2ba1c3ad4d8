﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.OutboxHistories
{
    public class AddOutboxHistoryUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IAddOutboxHistoryUseCase
    {
        public async Task AddAsync(Guid outboxId, bool success, string message)
        {
            var repository = GetGenericRepository<OutboxHistory>();
            await repository.AddAsync(new OutboxHistory()
            {
                IsSuccess = success,
                Message = message,
                OutboxId = outboxId,
                TimeExecuted = DateTime.UtcNow,
            });

            await CommitAsync();
        }
    }
}