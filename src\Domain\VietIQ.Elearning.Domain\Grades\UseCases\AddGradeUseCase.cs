﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Grades
{
    public class AddGradeUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IAddGradeUseCase
    {
        public async Task<IApiResponse> AddAsync(AddGradeInput input)
        {
            var repo = GetGenericRepository<Grade>();

            var existedGrade = await repo.FindAsync(x => x.Name == input.Name || x.Code == input.Code);
            if (existedGrade != null)
            {
                return ApiResponse.BadRequest(MessageDefinition.AddFailed)
                                  .AppendErrors(nameof(input.Name), MessageDefinition.Grade.GradeNameDuplicated)
                                  .AppendErrors(nameof(input.Code), MessageDefinition.Grade.GradeCodeDuplicated);
            }

            var entity = Map<Grade>(input);
            await repo.AddAsync(entity);
            await CommitAsync();

            var output = Mapper.Map<GradeModel>(entity);
            return ApiResponse.Ok(output, MessageDefinition.Succeeded);
        }
    }
}
