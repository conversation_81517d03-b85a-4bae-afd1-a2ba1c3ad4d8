﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace VietIQ.Elearning.JwtBearer
{
    public static class AddJwtExtensions
    {
        public static void AddJwt(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.AddTransient<IJwtProvider, JwtProvider>();
            services.AddSingleton<ITokenBlacklistService, RedisTokenBlacklistService>();
            services.AddJwtConfig(configuration, environment);
        }
    }
}
