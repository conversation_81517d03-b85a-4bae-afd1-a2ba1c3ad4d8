﻿using VietIQ.Elearning.Api.InputValidation;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Core.Users
{
    public class UpdateUserInput
    {
        [FdMaxLength(EntityLength.Tiny)]
        public string? PhoneNumber { get; set; }

        [FdMaxLength(EntityLength.Short)]
        public string? FirstName { get; set; }

        [FdMaxLength(EntityLength.Short)]
        public string? LastName { get; set; }

        [FdMaxLength(EntityLength.Normal)]
        public string? Address { get; set; }

        public int? OrganizationId { get; set; }

        public int? ClassId { get; set; }

        public IEnumerable<string>? RoleNames { get; set; }
    }
}