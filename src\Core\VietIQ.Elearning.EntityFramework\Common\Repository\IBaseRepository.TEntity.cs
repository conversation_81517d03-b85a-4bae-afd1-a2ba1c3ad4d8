﻿using System.Linq.Expressions;

namespace VietIQ.Elearning.EntityFramework
{
    public interface IBaseRepository<TEntity> : IBaseRepository where TEntity : class, IEntity
    {
        IQueryable<TEntity> AsQueryable();

        Task<TEntity?> FindAsync(Expression<Func<TEntity, bool>> filter);

        Task<TEntity?> FindWithIncludesAsync(Expression<Func<TEntity, bool>> filter,
            params Expression<Func<TEntity, object>>[] navigationPropertyPaths);

        Task<TEntity?> FindAsync<TProperty>(Expression<Func<TEntity, bool>> filter,
                                            Expression<Func<TEntity, TProperty>> orderByPath,
                                            int sortOrder);

        Task<IEnumerable<TEntity>> GetAsync(Expression<Func<TEntity, bool>>? filter = null);

        Task<int> CountAsync(Expression<Func<TEntity, bool>>? filter = null);

        Task<IEnumerable<TEntity>> GetAsync<TProperty>(Expression<Func<TEntity, bool>>? filter = null,
            params Expression<Func<TEntity, TProperty>>[] navigationPropertyPaths);

        Task<IEnumerable<TEntity>?> GetWithIncludesAsync(Expression<Func<TEntity, bool>>? filter = null,
                                                         params Expression<Func<TEntity, object>>[] navigationPropertyPaths);

        Task<bool> AnyAsync(Expression<Func<TEntity, bool>>? filter = null);

        Task<TEntity?> AddAsync(TEntity entity);

        Task AddRangeAsync(IEnumerable<TEntity> entities);

        void Update(TEntity entity);

        void UpdateRange(IEnumerable<TEntity> entities);

        void Delete(TEntity entity);

        void DeleteRange(IEnumerable<TEntity> entities);

        void DeleteRange(Expression<Func<TEntity, bool>> expression);
    }
}
