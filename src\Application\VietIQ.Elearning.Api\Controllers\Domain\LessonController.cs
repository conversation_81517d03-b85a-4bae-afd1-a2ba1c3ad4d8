﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.Exercises;
using VietIQ.Elearning.Domain.Lessons;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers
{
    [ApiController]
    [Route("api/lessons")]
    [FeatureAuthorize]
    public class LessonController : ApiBaseController
    {
        private readonly IAddLessonUseCase addUseCase;
        private readonly IUpdateLessonUseCase updateUseCase;
        private readonly IDeleteLessonUseCase deleteUseCase;
        private readonly IQueryLessonUseCase queryUseCase;
        private readonly IQueryExercisesByLessonIdUseCase queryExercisesByLessonIdUseCase;

        public LessonController(IAddLessonUseCase addUseCase,
                                IUpdateLessonUseCase updateUseCase,
                                IDeleteLessonUseCase deleteUseCase,
                                IQueryLessonUseCase queryUseCase,
                                IQueryExercisesByLessonIdUseCase queryExercisesByLessonIdUseCase)
        {
            this.addUseCase = addUseCase;
            this.updateUseCase = updateUseCase;
            this.deleteUseCase = deleteUseCase;
            this.queryUseCase = queryUseCase;
            this.queryExercisesByLessonIdUseCase = queryExercisesByLessonIdUseCase;
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Lesson.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var response = await queryUseCase.GetAsync(input);
            return response.ToActionResult();
        }

        [HttpGet("{id}")]
        [FeatureAuthorize(PermissionNames.Lesson.View)]
        public async Task<IActionResult> GetByIdAsync(int id)
        {
            var response = await queryUseCase.GetByIdAsync(id);
            return response.ToActionResult();
        }

        [HttpGet("{lessonId}/exercises")]
        [FeatureAuthorize(PermissionNames.Lesson.View)]
        public async Task<IActionResult> GetExercisesByLessionIdAsync(int lessonId)
        {
            var response = await queryExercisesByLessonIdUseCase.ExecuteAsync(lessonId);
            return response.ToActionResult();
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Lesson.Add)]
        public async Task<IActionResult> Add([FromBody] AddLessonInput input)
        {
            var response = await addUseCase.AddAsync(input);
            return response.ToActionResult();
        }

        [HttpPut("{id}")]
        [FeatureAuthorize(PermissionNames.Lesson.Update)]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLessonInput input)
        {
            var response = await updateUseCase.UpdateAsync(id, input);
            return response.ToActionResult();
        }

        [HttpDelete("{id}")]
        [FeatureAuthorize(PermissionNames.Lesson.Delete)]
        public async Task<IActionResult> Delete(int id)
        {
            var response = await deleteUseCase.DeleteAsync(id);
            return response.ToActionResult();
        }
    }
}
