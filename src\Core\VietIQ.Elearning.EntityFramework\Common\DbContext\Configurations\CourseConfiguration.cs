﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class CourseConfiguration : IEntityTypeConfiguration<Course>
    {
        public void Configure(EntityTypeBuilder<Course> builder)
        {
            builder.Has<PERSON>ey(c => c.Id);

            builder.HasIndex(c => c.GradeId)
                   .HasDatabaseName("IX_Course_GradeId");

            builder.HasMany(l => l.Weeks)
                   .WithOne(e => e.Course)
                   .HasForeignKey(e => e.CourseId);
        }
    }
}
