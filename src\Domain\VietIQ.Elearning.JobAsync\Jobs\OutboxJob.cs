﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using VietIQ.Elearning.Domain.Outboxes;
using VietIQ.Elearning.Domain.OutboxHistories;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.JobAsync
{
    [DisallowConcurrentExecution]
    public class OutboxJob : IJob
    {
        private readonly ILogger<OutboxJob> _logger;
        private readonly IServiceScopeFactory serviceScopeFactory;

        public OutboxJob(ILogger<OutboxJob> logger, IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            this.serviceScopeFactory = serviceScopeFactory;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            using var scope = serviceScopeFactory.CreateScope();
            var queryOutboxUseCase = scope.ServiceProvider.GetService<IQueryOutboxUseCase>()!;
            var addOutboxHistoryUseCase = scope.ServiceProvider.GetService<IAddOutboxHistoryUseCase>()!;

            var incompleteOuboxes = await queryOutboxUseCase.GetIncompleteTasksAsync();
            foreach (var outbox in incompleteOuboxes)
            {
                bool isSuccess = false;
                string? message;

                try
                {
                    var response = await this.ExecuteCommandAsync(scope, outbox);
                    isSuccess = response.IsSuccess;
                    message = response.Message;
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                }

                try
                {
                    // Xử lý ghi nhận log vào trong Database
                    message = isSuccess ? "SUCCESS" : message ?? "ERROR";
                    await addOutboxHistoryUseCase.AddAsync(outbox.Id, isSuccess, message);
                }
                catch
                {
                    // TODO: Ghi log
                }
            }
        }

        /// <summary>
        /// Thực hiện command dựa vào outbox đầu vào
        /// </summary>
        private async Task<IApiResponse> ExecuteCommandAsync(IServiceScope serviceScope, Outbox outbox)
        {
            switch (outbox.CommandName)
            {
                case OutboxCommand.AddScore:
                    {
                        var service = serviceScope.ServiceProvider.GetService<IProcessAddScoreUseCase>()!;
                        return await service.ExecuteAsync(outbox);
                    }
                default:
                    {
                        throw new NotImplementedException("Could not found command name: " + outbox.CommandName);
                    }
            }
        }
    }
}
