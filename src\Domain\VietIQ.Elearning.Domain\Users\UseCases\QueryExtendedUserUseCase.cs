﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Users
{
    public class QueryExtendedUserUseCase(IUnitOfWork unitOfWork,
                                          IMapper mapper) : BaseService(unitOfWork, mapper), IQueryExtendedUserUseCase
    {
        public async Task<IApiResponse<ExtendedUserModel>> GetExtendedUserByIdAsync(int id,
                                                                                    CancellationToken cancellationToken = default)
        {
            var repository = this.GetGenericRepository<User>();
            var user = await repository.FindWithIncludesAsync(x => x.Id == id, x => x.Class!);
            if (user == null)
            {
                return ApiResponse.NotFound<ExtendedUserModel>(MessageDefinition.NotFound);
            }

            var userModel = Mapper.Map<ExtendedUserModel>(user);
            return ApiResponse.Ok(userModel, MessageDefinition.Succeeded);
        }

        public async Task<IApiResponse<ExtendedUserModel>> GetExtendedUserByTokenAsync(string token,
                                                                                       CancellationToken cancellationToken = default)
        {
            var repository = this.GetGenericRepository<User>();
            var user = await repository.FindWithIncludesAsync(x => x.Token == token, x => x.Class!);
            if (user == null)
            {
                return ApiResponse.NotFound<ExtendedUserModel>(MessageDefinition.NotFound);
            }

            var userModel = Mapper.Map<ExtendedUserModel>(user);
            return ApiResponse.Ok(userModel, MessageDefinition.Succeeded);
        }

        public async Task<IApiResponse> GetExtendedUsersAsync(IPaginationInput paginationInput,
                                                               CancellationToken cancellationToken = default)
        {
            var repository = GetGenericRepository<User, int>();

            var searchPredicate = PredicateBuilder.False<User>();
            if (paginationInput.HasSearchTerm)
            {
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.UserName, paginationInput.SearchTermPattern));
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.Email, paginationInput.SearchTermPattern));
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.FirstName!, paginationInput.SearchTermPattern));
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.LastName!, paginationInput.SearchTermPattern));
            }

            var users = await repository
                                .AsQueryable()
                                .WhereIf(paginationInput.HasSearchTerm, searchPredicate)
                                .Include(x => x.Class!)
                                .ThenInclude(x => x.Organization!)
                                .ToPaginationAsync(paginationInput);

            var userModels = MapPagination<User, ExtendedUserModel>(users);
            return userModels.ToApiResponse();
        }
    }
}
