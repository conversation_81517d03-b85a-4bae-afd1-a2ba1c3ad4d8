﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Exercises")]
    public class Exercise : FullyEntity<int>
    {
        public int LessonId { get; set; }

        public int TypeId { get; set; }

        public string Code { get; set; } = null!;

        public string Title { get; set; } = null!;

        public string? QuestionData { get; set; }

        public string? MetaData { get; set; }

        public string? Explanation { get; set; }

        public DisplayDirection DisplayDirection { get; set; } = DisplayDirection.Vertical;

        public virtual Lesson Lesson { get; set; } = null!;
        public virtual ExerciseType ExerciseType { get; set; } = null!;
        public virtual List<ExerciseTag> ExerciseTags { get; set; } = [];
        public virtual List<UserAnswer> UserAnswers { get; set; } = [];
        public virtual List<AIRecommendation> AIRecommendations { get; set; } = [];
        public virtual List<ExerciseItem> ExerciseItems { get; set; } = [];
        public virtual List<ExerciseAnswer> ExerciseAnswers { get; set; } = [];
        public virtual List<PronunciationError> PronunciationErrors { get; set; } = [];
    }
}
