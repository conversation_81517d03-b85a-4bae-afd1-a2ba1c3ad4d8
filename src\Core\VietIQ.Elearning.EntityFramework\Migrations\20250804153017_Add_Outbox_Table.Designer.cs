﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using VietIQ.Elearning.EntityFramework;

#nullable disable

namespace VietIQ.Elearning.EntityFramework.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250804153017_Add_Outbox_Table")]
    partial class Add_Outbox_Table
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("AIRecommendationExercise", b =>
                {
                    b.Property<Guid>("AIRecommendationsId")
                        .HasColumnType("uuid");

                    b.Property<int>("ExercisesId")
                        .HasColumnType("integer");

                    b.HasKey("AIRecommendationsId", "ExercisesId");

                    b.HasIndex("ExercisesId");

                    b.ToTable("AIRecommendationExercise");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("Core_RoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Core_UserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("Core_UserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("Core_UserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("Core_UserTokens", (string)null);
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.AIRecommendation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("GeneratedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SuggestedExercises")
                        .HasColumnType("text");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AIRecommendations");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ApiToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ApiTokens");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Class", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AcademicYear")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("GradeId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("GradeId");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Class_Name");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_Class_OrganizationId");

                    b.ToTable("Classes");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Course", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CourseType")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("GradeId")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("GradeId")
                        .HasDatabaseName("IX_Course_GradeId");

                    b.ToTable("Courses");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Domain.PronunciationResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AudioFileUrl")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int?>("ExerciseId")
                        .HasColumnType("integer");

                    b.Property<string>("Feedback")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("PhonemeScoreStatistics")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<double?>("ProcessingTime")
                        .HasColumnType("double precision");

                    b.Property<string>("RequestId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Score")
                        .HasColumnType("integer");

                    b.Property<double?>("SpeakingDuration")
                        .HasColumnType("double precision");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("StressResponse")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserPhoneme")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<double?>("WordsPerMin")
                        .HasColumnType("double precision");

                    b.Property<string>("WordsScoreDetail")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.HasKey("Id");

                    b.HasIndex("ExerciseId");

                    b.HasIndex("UserId");

                    b.ToTable("PronunciationResults");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Exercise", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisplayDirection")
                        .HasColumnType("integer");

                    b.Property<string>("Explanation")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<int>("LessonId")
                        .HasColumnType("integer");

                    b.Property<string>("MetaData")
                        .HasColumnType("jsonb");

                    b.Property<string>("QuestionData")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TypeId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("LessonId")
                        .HasDatabaseName("IX_Exercise_LessonId");

                    b.HasIndex("TypeId");

                    b.ToTable("Exercises");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseAnswer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("ExerciseId")
                        .HasColumnType("integer");

                    b.Property<string>("Explanation")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsCorrect")
                        .HasColumnType("boolean");

                    b.Property<string>("MatchKey")
                        .HasColumnType("text");

                    b.Property<Guid?>("MediaFileId")
                        .HasColumnType("uuid");

                    b.Property<int>("MediaType")
                        .HasColumnType("integer");

                    b.Property<int?>("OrderIndex")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExerciseId");

                    b.HasIndex("MediaFileId");

                    b.ToTable("ExerciseAnswers");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("ExerciseId")
                        .HasColumnType("integer");

                    b.Property<Guid?>("MediaFileId")
                        .HasColumnType("uuid");

                    b.Property<int>("MediaType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExerciseId");

                    b.HasIndex("MediaFileId");

                    b.ToTable("ExerciseItems");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseTag", b =>
                {
                    b.Property<int>("ExerciseId")
                        .HasColumnType("integer");

                    b.Property<int>("TagId")
                        .HasColumnType("integer");

                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.HasKey("ExerciseId", "TagId");

                    b.HasIndex("TagId");

                    b.ToTable("ExerciseTags");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ScoringRules")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ExerciseTypes");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Grade", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Grade_Name");

                    b.ToTable("Grades");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Lesson", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("WeekId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("WeekId")
                        .HasDatabaseName("IX_Lesson_WeekId");

                    b.ToTable("Lessons");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.MediaFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("MediaFiles");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsGlobal")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Core_Notifications");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.OperationLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("Ip")
                        .HasColumnType("text");

                    b.Property<string>("JsonResult")
                        .HasColumnType("text");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("RequestMethod")
                        .HasColumnType("text");

                    b.Property<string>("RequestParam")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Core_OperationLogs");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Organization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Organization_Name");

                    b.ToTable("Organizations");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsGranted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("RoleId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.ToTable("Core_Permissions");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.PronunciationError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CorrectPhonetic")
                        .HasColumnType("text");

                    b.Property<DateTime>("ErrorTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExerciseId")
                        .HasColumnType("integer");

                    b.Property<string>("TargetWord")
                        .HasColumnType("text");

                    b.Property<string>("UserAttempt")
                        .HasColumnType("text");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExerciseId");

                    b.HasIndex("UserId");

                    b.ToTable("PronunciationErrors");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.RefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReasonRevoked")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("ReplacedByToken")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("RevokedByIp")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("RevokedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Token")
                        .HasDatabaseName("IX_RefreshTokens_Token");

                    b.HasIndex("UserId");

                    b.ToTable("Core_RefreshTokens");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("Core_Roles", (string)null);
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Setting", b =>
                {
                    b.Property<string>("Key")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Value")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.HasKey("Key");

                    b.HasIndex("Key")
                        .HasDatabaseName("Idx_Key");

                    b.ToTable("Core_Settings");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Subscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("OrganizationId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Subscription_Name");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_Subscription_OrganizationId");

                    b.ToTable("Subscriptions");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.SubscriptionClass", b =>
                {
                    b.Property<int>("SubscriptionId")
                        .HasColumnType("integer");

                    b.Property<int>("ClassId")
                        .HasColumnType("integer");

                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.HasKey("SubscriptionId", "ClassId");

                    b.HasIndex("ClassId");

                    b.ToTable("SubscriptionClasses");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.SubscriptionUser", b =>
                {
                    b.Property<int>("SubscriptionId")
                        .HasColumnType("integer");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("SubscriptionId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("SubscriptionUsers");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Tag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("TagDescription")
                        .HasColumnType("text");

                    b.Property<string>("TagName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("TagName")
                        .IsUnique()
                        .HasDatabaseName("IX_Tag_TagName");

                    b.ToTable("Tags");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.TestEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Tests");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("Address")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("ClassId")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FirstName")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastLoginTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MaxTokenCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("ClassId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("Token")
                        .HasDatabaseName("Idx_Token");

                    b.HasIndex("UserName")
                        .HasDatabaseName("Idx_Username");

                    b.ToTable("Core_Users", (string)null);
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.UserAnswer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AnswerId")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExerciseId")
                        .HasColumnType("integer");

                    b.Property<int>("ExerciseItemId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsCorrect")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSkipped")
                        .HasColumnType("boolean");

                    b.Property<int?>("PronunciationResultId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ResponseTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserResponse")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AnswerId");

                    b.HasIndex("ExerciseId");

                    b.HasIndex("ExerciseItemId");

                    b.HasIndex("PronunciationResultId")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("UserAnswers");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.UserNotification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("NotificationId");

                    b.HasIndex("UserId");

                    b.ToTable("Core_UserNotifications");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.UserProgress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Attempts")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CompeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<float>("CompletionPercentage")
                        .HasColumnType("real");

                    b.Property<int>("LessonId")
                        .HasColumnType("integer");

                    b.Property<int?>("Score")
                        .HasColumnType("integer");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("LessonId");

                    b.HasIndex("UserId", "LessonId")
                        .IsUnique()
                        .HasDatabaseName("IX_Progress_UserLesson");

                    b.ToTable("UserProgresses");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Week", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CourseId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("LearningObjectives")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("WeekNumber")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CourseId")
                        .HasDatabaseName("IX_Week_CourseId");

                    b.ToTable("Weeks");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.WeeklyStat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ErrorSummary")
                        .HasColumnType("text");

                    b.Property<int>("LessonCompleted")
                        .HasColumnType("integer");

                    b.Property<int>("StudyTimeInSeconds")
                        .HasColumnType("integer");

                    b.Property<int>("TotalAttempts")
                        .HasColumnType("integer");

                    b.Property<int>("TotalScore")
                        .HasColumnType("integer");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("WeekStart")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("WeeklyStats");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Outbox", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CommandName")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Outboxes");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.OutboxHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<Guid>("OutboxId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("TimeExecuted")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("OutboxId");

                    b.ToTable("OutboxHistories");
                });

            modelBuilder.Entity("AIRecommendationExercise", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.AIRecommendation", null)
                        .WithMany()
                        .HasForeignKey("AIRecommendationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", null)
                        .WithMany()
                        .HasForeignKey("ExercisesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.AIRecommendation", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("AIRecommendations")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ApiToken", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("ApiTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_User_Token");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Class", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Grade", "Grade")
                        .WithMany("Classes")
                        .HasForeignKey("GradeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Organization", "Organization")
                        .WithMany("Classes")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Grade");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Course", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Grade", "Grade")
                        .WithMany("Courses")
                        .HasForeignKey("GradeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Grade");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Domain.PronunciationResult", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", "Exercise")
                        .WithMany()
                        .HasForeignKey("ExerciseId");

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("PronunciationResults")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Exercise");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Exercise", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Lesson", "Lesson")
                        .WithMany("Exercises")
                        .HasForeignKey("LessonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.ExerciseType", "ExerciseType")
                        .WithMany("Exercises")
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ExerciseType");

                    b.Navigation("Lesson");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseAnswer", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", "Exercise")
                        .WithMany("ExerciseAnswers")
                        .HasForeignKey("ExerciseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.MediaFile", "MediaFile")
                        .WithMany()
                        .HasForeignKey("MediaFileId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Exercise");

                    b.Navigation("MediaFile");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseItem", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", "Exercise")
                        .WithMany("ExerciseItems")
                        .HasForeignKey("ExerciseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.MediaFile", "MediaFile")
                        .WithMany()
                        .HasForeignKey("MediaFileId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Exercise");

                    b.Navigation("MediaFile");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseTag", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", "Exercise")
                        .WithMany("ExerciseTags")
                        .HasForeignKey("ExerciseId");

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Tag", "Tag")
                        .WithMany("ExerciseTags")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Exercise");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Lesson", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Week", "Week")
                        .WithMany("Lessons")
                        .HasForeignKey("WeekId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Week");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Permission", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId");

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("Permissions")
                        .HasForeignKey("UserId");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.PronunciationError", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", "Exercise")
                        .WithMany("PronunciationErrors")
                        .HasForeignKey("ExerciseId");

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("PronunciationErrors")
                        .HasForeignKey("UserId");

                    b.Navigation("Exercise");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.RefreshToken", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_User_RefreshToken");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Subscription", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Organization", "Organization")
                        .WithMany("Subscriptions")
                        .HasForeignKey("OrganizationId");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.SubscriptionClass", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Class", "Class")
                        .WithMany()
                        .HasForeignKey("ClassId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Subscription", "Subscription")
                        .WithMany("SubscriptionClasses")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Class");

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.SubscriptionUser", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Subscription", "Subscription")
                        .WithMany("SubscriptionUsers")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("SubscriptionUsers")
                        .HasForeignKey("UserId");

                    b.Navigation("Subscription");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.User", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Class", "Class")
                        .WithMany("Users")
                        .HasForeignKey("ClassId");

                    b.Navigation("Class");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.UserAnswer", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.ExerciseAnswer", "Answer")
                        .WithMany()
                        .HasForeignKey("AnswerId");

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Exercise", "Exercise")
                        .WithMany("UserAnswers")
                        .HasForeignKey("ExerciseId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.ExerciseItem", "ExerciseItem")
                        .WithMany()
                        .HasForeignKey("ExerciseItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Domain.PronunciationResult", "PronunciationResult")
                        .WithOne("UserAnswer")
                        .HasForeignKey("VietIQ.Elearning.EntityFramework.Entities.UserAnswer", "PronunciationResultId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("UserAnswers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Answer");

                    b.Navigation("Exercise");

                    b.Navigation("ExerciseItem");

                    b.Navigation("PronunciationResult");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.UserNotification", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Notification", "Notification")
                        .WithMany("UserNotifications")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("UserNotifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Notification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.UserProgress", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Lesson", "Lesson")
                        .WithMany("Progresses")
                        .HasForeignKey("LessonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("Progresses")
                        .HasForeignKey("UserId");

                    b.Navigation("Lesson");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Week", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.Course", "Course")
                        .WithMany("Weeks")
                        .HasForeignKey("CourseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Course");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.WeeklyStat", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Entities.User", "User")
                        .WithMany("WeeklyStats")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.OutboxHistory", b =>
                {
                    b.HasOne("VietIQ.Elearning.EntityFramework.Outbox", "Outbox")
                        .WithMany("Histories")
                        .HasForeignKey("OutboxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Outbox_History");

                    b.Navigation("Outbox");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Class", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Course", b =>
                {
                    b.Navigation("Weeks");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Domain.PronunciationResult", b =>
                {
                    b.Navigation("UserAnswer");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Exercise", b =>
                {
                    b.Navigation("ExerciseAnswers");

                    b.Navigation("ExerciseItems");

                    b.Navigation("ExerciseTags");

                    b.Navigation("PronunciationErrors");

                    b.Navigation("UserAnswers");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.ExerciseType", b =>
                {
                    b.Navigation("Exercises");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Grade", b =>
                {
                    b.Navigation("Classes");

                    b.Navigation("Courses");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Lesson", b =>
                {
                    b.Navigation("Exercises");

                    b.Navigation("Progresses");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Notification", b =>
                {
                    b.Navigation("UserNotifications");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Organization", b =>
                {
                    b.Navigation("Classes");

                    b.Navigation("Subscriptions");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Subscription", b =>
                {
                    b.Navigation("SubscriptionClasses");

                    b.Navigation("SubscriptionUsers");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Tag", b =>
                {
                    b.Navigation("ExerciseTags");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.User", b =>
                {
                    b.Navigation("AIRecommendations");

                    b.Navigation("ApiTokens");

                    b.Navigation("Permissions");

                    b.Navigation("Progresses");

                    b.Navigation("PronunciationErrors");

                    b.Navigation("PronunciationResults");

                    b.Navigation("RefreshTokens");

                    b.Navigation("SubscriptionUsers");

                    b.Navigation("UserAnswers");

                    b.Navigation("UserNotifications");

                    b.Navigation("WeeklyStats");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Entities.Week", b =>
                {
                    b.Navigation("Lessons");
                });

            modelBuilder.Entity("VietIQ.Elearning.EntityFramework.Outbox", b =>
                {
                    b.Navigation("Histories");
                });
#pragma warning restore 612, 618
        }
    }
}
