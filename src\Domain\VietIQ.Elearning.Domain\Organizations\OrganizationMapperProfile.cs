﻿using AutoMapper;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Organizations
{
    public class OrganizationMapperProfile : Profile
    {
        public OrganizationMapperProfile()
        {
            CreateMap<AddOrganizationInput, Organization>().ReverseMap();
            CreateMap<UpdateOrganizationInput, Organization>().ReverseMap();
            CreateMap<Organization, OrganizationModel>().ForMember(dest => dest.Classes, opt => opt.MapFrom(src => src.Classes));
        }
    }
}
