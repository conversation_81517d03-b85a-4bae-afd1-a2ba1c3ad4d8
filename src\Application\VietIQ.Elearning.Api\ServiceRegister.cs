﻿using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.AutoDependency;
using VietIQ.Elearning.Api.Shared.Configurations;
using VietIQ.Elearning.DynamicApi;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.Infrastructure.Caching;
using VietIQ.Elearning.Infrastructure.Email;
using VietIQ.Elearning.JobAsync;
using VietIQ.Elearning.JwtBearer;

namespace VietIQ.Elearning.Api
{
    public static class ServiceRegister
    {
        public static void Register(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            RegisterDependencies(services, configuration, environment);

            services.AddScoped<IUnitOfWork, UnitOfWork>();
        }

        private static void RegisterDependencies(IServiceCollection services,
                                                 IConfiguration configuration,
                                                 IWebHostEnvironment environment)
        {
            // Add more configurations
            services.AddCache(CacheMethod.Redis, configuration);

            // Add response caching
            services.AddResponseCaching();

            // Add permission based feature
            services.AddPermissionsBasedAuthorize();

            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            services.AddEndpointsApiExplorer();
            services.AddSwagger();

            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

            // Add HttpContextAccessor
            services.AddHttpContextAccessor();

            // Add CORS for api
            services.AddCustomCors();

            // Add email template sender
            services.AddEmailSender();

            services.AddJwt(configuration, environment);

            // Add quartz job
            services.AddQuartzJob();

            // Add auto dependencies 
            services.AddAutoDependency();

            // Add Rate Limiting
            //builder.Services.AddRateLimiting(builder.Configuration);

            // dynamic api
            services.AddDynamicApiServices(typeof(IDynamicApi).Assembly);

            services.AddBackgroundService();
        }
    }
}
