﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace VietIQ.Elearning.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class Update_Exercises_Answers_Columns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MatchKey",
                table: "ExerciseAnswers",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OrderIndex",
                table: "ExerciseAnswers",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Match<PERSON><PERSON>",
                table: "ExerciseAnswers");

            migrationBuilder.DropColumn(
                name: "OrderIndex",
                table: "ExerciseAnswers");
        }
    }
}
