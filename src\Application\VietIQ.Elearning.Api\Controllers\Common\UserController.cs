﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Core.Users;
using VietIQ.Elearning.Domain.Users;
using VietIQ.Elearning.DynamicApi.Response;

namespace VietIQ.Elearning.Api.Shared.Controllers
{
    /// <summary>
    /// Controller for managing user-related operations.
    /// </summary>
    [Route("api/users")]
    [FeatureAuthorize]
    public class UserController : ApiBaseController
    {
        private readonly IAddUserUseCase addUserUseCase;
        private readonly IUpdateUserUseCase updateUserUseCase;
        private readonly IDeleteUserUseCase deleteUserUseCase;
        private readonly IQueryUserUseCase queryUserUseCase;
        private readonly IQueryExtendedUserUseCase queryExtendedUserUseCase;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserController"/> class.
        /// </summary>
        /// <param name="addUserUseCase">The use case for adding users.</param>
        /// <param name="updateUserUseCase">The use case for updating users.</param>
        /// <param name="deleteUserUseCase">The use case for deleting users.</param>
        /// <param name="queryUserUseCase">The use case for querying users.</param>
        /// <param name="queryExtendedUserUseCase">The use case for querying all users.</param>
        public UserController(
            IAddUserUseCase addUserUseCase,
            IUpdateUserUseCase updateUserUseCase,
            IDeleteUserUseCase deleteUserUseCase,
            IQueryUserUseCase queryUserUseCase,
            IQueryExtendedUserUseCase queryExtendedUserUseCase)
        {
            this.addUserUseCase = addUserUseCase;
            this.updateUserUseCase = updateUserUseCase;
            this.deleteUserUseCase = deleteUserUseCase;
            this.queryUserUseCase = queryUserUseCase;
            this.queryExtendedUserUseCase = queryExtendedUserUseCase;
        }

        /// <summary>
        /// Adds a new user.
        /// </summary>
        /// <param name="input">The input data for adding a user.</param>
        /// <returns>The result of the add operation.</returns>
        [HttpPost]
        [FeatureAuthorize(PermissionNames.User.Add)]
        public async Task<IActionResult> AddAsync([FromBody] AddUserInput input)
        {
            var response = await this.addUserUseCase.AddAsync(input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Updates an existing user.
        /// </summary>
        /// <param name="userId">The ID of the user to update.</param>
        /// <param name="input">The input data for updating the user.</param>
        /// <returns>The result of the update operation.</returns>
        [HttpPut("{userId}")]
        [FeatureAuthorize(PermissionNames.User.Update)]
        public async Task<IActionResult> UpdateAsync(int userId, [FromBody] UpdateUserInput input)
        {
            var response = await this.updateUserUseCase.UpdateAsync(userId, input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Updates an existing user with a file.
        /// </summary>
        /// <param name="userId">The ID of the user to update.</param>
        /// <param name="input">The input data for updating the user.</param>
        /// <returns>The result of the update operation.</returns>
        [HttpPut("{userId}/with-file")]
        [FeatureAuthorize(PermissionNames.User.Update)]
        public async Task<IActionResult> UpdateWithFileAsync(int userId, [FromForm] UpdateUserInput input)
        {
            var response = await this.updateUserUseCase.UpdateAsync(userId, input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Updates the current user's information.
        /// </summary>
        /// <param name="input">The input data for updating the user.</param>
        /// <returns>The result of the update operation.</returns>
        [HttpPut("me")]
        [FeatureAuthorize]
        public async Task<IActionResult> UpdateSelfAsync([FromBody] UpdateUserInput input)
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return ApiResponse.Unauthorized().ToActionResult();
            }

            var response = await this.updateUserUseCase.UpdateAsync(userId.Value, input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Changes the password of a user.
        /// </summary>
        /// <param name="userId">The ID of the user whose password is to be changed.</param>
        /// <param name="input">The input data for changing the password.</param>
        /// <returns>The result of the password change operation.</returns>
        [HttpPatch("{userId}/password")]
        [FeatureAuthorize(PermissionNames.User.Update)]
        public async Task<IActionResult> ChangePasswordAsync(int userId, [FromBody] ResetPasswordInput input)
        {
            var response = await this.updateUserUseCase.ResetPasswordAsync(userId, input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Changes the current user's password.
        /// </summary>
        /// <param name="input">The input data for changing the password.</param>
        /// <returns>The result of the password change operation.</returns>
        [HttpPatch("me/password")]
        [FeatureAuthorize]
        public async Task<IActionResult> ChangePasswordAsync([FromBody] ChangePasswordInput input)
        {
            var userId = this.GetUserId();
            if (userId == null)
            {
                return ApiResponse.Unauthorized().ToActionResult();
            }

            var response = await this.updateUserUseCase.ChangePasswordAsync(userId.Value, input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Updates the maximum token count for a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="tokenMaxCount">The new maximum token count.</param>
        /// <returns>The result of the update operation.</returns>
        [HttpPatch("{userId}/max-token")]
        [FeatureAuthorize(PermissionNames.User.UpdateMaxToken)]
        public async Task<IActionResult> UpdateMaxTokenAsync(int userId, int tokenMaxCount)
        {
            var response = await this.updateUserUseCase.UpdateMaxTokenAsync(userId, tokenMaxCount);
            return response.ToActionResult();
        }

        /// <summary>
        /// Updates the enable state of a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="isEnable">The new enable state.</param>
        /// <returns>The result of the update operation.</returns>
        [HttpPatch("{userId}/enable")]
        [FeatureAuthorize(PermissionNames.User.Update)]
        public async Task<IActionResult> UpdateEnableStateAsync(int userId, bool isEnable)
        {
            var response = await this.updateUserUseCase.UpdateEnableStateAsync(userId, isEnable);
            return response.ToActionResult();
        }

        /// <summary>
        /// Updates the enable states of multiple users.
        /// </summary>
        /// <param name="input">The input data for updating the enable states.</param>
        /// <returns>The result of the update operation.</returns>
        [HttpPatch("enables")]
        [FeatureAuthorize(PermissionNames.User.Update)]
        public async Task<IActionResult> UpdateMultiEnableStatesAsync([FromBody] UpdateUsersStatusInput input)
        {
            var response = await this.updateUserUseCase.UpdateMultiEnableStatesAsync(input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Verifies the email of a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="input">The input data for verifying the email.</param>
        /// <returns>The result of the verification operation.</returns>
        [HttpPatch("{userId}/verify-email")]
        [FeatureAuthorize(PermissionNames.User.Update)]
        public async Task<IActionResult> VerifyEmailAsync(int userId, [FromBody] ConfirmEmailInput input)
        {
            var response = await this.updateUserUseCase.VerifyEmailAsync(userId, input);
            return response.ToActionResult();
        }

        /// <summary>
        /// Deletes a user.
        /// </summary>
        /// <param name="userId">The ID of the user to delete.</param>
        /// <returns>The result of the delete operation.</returns>
        [HttpDelete("{userId}")]
        [FeatureAuthorize(PermissionNames.User.Delete)]
        public async Task<IActionResult> DeleteAsync(int userId)
        {
            var response = await this.deleteUserUseCase.DeleteAsync(userId);
            return response.ToActionResult();
        }

        /// <summary>
        /// Deletes multiple users.
        /// </summary>
        /// <param name="userIds">The IDs of the users to delete.</param>
        /// <returns>The result of the delete operation.</returns>
        [HttpPost("delete-all")]
        [FeatureAuthorize(PermissionNames.User.Delete)]
        public async Task<IActionResult> DeleteAllAsync([FromBody] ICollection<int> userIds)
        {
            var response = await this.deleteUserUseCase.DeleteAllAsync(userIds);
            return response.ToActionResult();
        }

        /// <summary>
        /// Gets a paginated list of users.
        /// </summary>
        /// <param name="input">The pagination input data.</param>
        /// <returns>The result of the get operation.</returns>
        [HttpGet]
        [FeatureAuthorize(PermissionNames.User.View)]
        public async Task<IActionResult> GetAsync(UserPaginationInput input)
        {
            var result = await queryUserUseCase.GetAsync(input);
            return result.ToActionResult();
        }

        /// <summary>
        /// Gets a user by ID.
        /// </summary>
        /// <param name="id">The ID of the user to get.</param>
        /// <returns>The result of the get operation.</returns>
        [HttpGet("{id}")]
        [FeatureAuthorize(PermissionNames.User.View)]
        public async Task<IActionResult> GetByIdAsync(int id)
        {
            var result = await queryUserUseCase.GetByIdAsync(id);
            return result.ToActionResult();
        }

        /// <summary>
        /// Gets a extented user by ID.
        /// </summary>
        /// <param name="id">The ID of the user to get.</param>
        /// <returns>The result of the get operation.</returns>
        [HttpGet("{id}/extended")]
        [FeatureAuthorize(PermissionNames.User.View)]
        public async Task<IActionResult> GetExtendedUserByIdAsync(int id)
        {
            var result = await queryExtendedUserUseCase.GetExtendedUserByIdAsync(id);
            return result.ToActionResult();
        }

        /// <summary>
        /// Gets a paginated list of extended users.
        /// </summary>
        /// <param name="input">The pagination input data.</param>
        /// <returns>The result of the get operation.</returns>
        [HttpGet("extended")]
        [FeatureAuthorize(PermissionNames.User.View)]
        public async Task<IActionResult> GetExtendedUsersAsync(UserPaginationInput input)
        {
            var result = await queryExtendedUserUseCase.GetExtendedUsersAsync(input);
            return result.ToActionResult();
        }

        /// <summary>
        /// Gets the profile of the current user.
        /// </summary>
        /// <returns>The result of the get operation.</returns>
        [HttpGet("profile")]
        [FeatureAuthorize]
        public async Task<IActionResult> GetUserProfile()
        {
            var userId = this.GetUserId();
            if (!userId.HasValue)
            {
                return ApiResponse.Unauthorized().ToActionResult();
            }

            var result = await queryUserUseCase.GetByIdAsync(userId.Value);
            return result.ToActionResult();
        }
    }
}
