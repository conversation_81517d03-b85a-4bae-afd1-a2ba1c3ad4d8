﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Grades
{
    public class QueryAllGradeUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IQueryAllGradeUseCase
    {
        public async Task<IApiResponse> ExecuteAsync()
        {
            var repo = GetGenericRepository<Grade, int>();
            var entities = await repo.GetAsync();
            if (entities == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Grade.GradeNotFound);
            }

            return ApiResponse.Ok(Mapper.Map<IEnumerable<GradeModel>>(entities));
        }
    }
}
