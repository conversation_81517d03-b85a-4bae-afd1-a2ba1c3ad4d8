﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("ExerciseAnswers")]
    public class ExerciseAnswer : Entity<int>
    {
        public int ExerciseId { get; set; }

        public Guid? MediaFileId { get;set; }

        public MediaType MediaType { get; set; }

        public string? Content { get; set; }

        public bool IsCorrect { get; set; }

        public int DisplayOrder { get; set; }

        public int? OrderIndex { get; set; } 

        public string? MatchKey { get; set; }

        public string? Explanation { get; set; }

        public virtual Exercise Exercise{ get; set; } = null!;

        public virtual  MediaFile? MediaFile { get; set; }
    }
}
