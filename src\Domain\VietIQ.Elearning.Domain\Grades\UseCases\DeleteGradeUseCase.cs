﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Grades
{
    public class DeleteGradeUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IDeleteGradeUseCase
    {
        public async Task<IApiResponse> DeleteAsync(int id)
        {
            var repo = GetGenericRepository<Grade, int>();
            repo.Delete(id);

            await CommitAsync();

            return ApiResponse.Ok(MessageDefinition.Succeeded);
        }
    }
}
