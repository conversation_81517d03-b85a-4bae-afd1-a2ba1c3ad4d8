﻿namespace VietIQ.Elearning.Core.Constants
{
    public class SettingName
    {
        public class Basic
        {
            public const string GroupName = nameof(Basic);

            public const string SiteName = nameof(SiteName);
        }

        public class ConfirmAccount
        {
            public const string GroupName = nameof(ConfirmAccount);

            public const string Enable = nameof(Enable);

            public const string FrontendUrl = nameof(FrontendUrl);

            public const string BackendUrl = nameof(BackendUrl);

            public const string EmailTitle = nameof(EmailTitle);

            public const string EmailTemplate = nameof(EmailTemplate);
        }

        public class InformationAccount
        {
            public const string GroupName = nameof(InformationAccount);

            public const string Enable = nameof(Enable);

            public const string FrontendUrl = nameof(FrontendUrl);

            public const string BackendUrl = nameof(BackendUrl);

            public const string EmailTitle = nameof(EmailTitle);

            public const string EmailTemplate = nameof(EmailTemplate);
        }

        public class Smtp
        {
            public const string GroupName = nameof(Smtp);

            public const string Host = nameof(Host);

            public const string Port = nameof(Port);

            public const string Name = nameof(Name);

            public const string From = nameof(From);

            public const string Username = nameof(Username);

            public const string Password = nameof(Password);

            public const string SecureSocket = nameof(SecureSocket);
        }

        public class Authentication
        {
            public const string GroupName = nameof(Authentication);

            public const string TokenExpired = nameof(TokenExpired);

            public const string RefreshTokenExpired = nameof(RefreshTokenExpired);
        }

        public class RateLimit
        {
            public const string GroupName = nameof(RateLimit);

            public const string UserDefaultPoint = nameof(UserDefaultPoint);

            public const string DefaultMinusPoint = nameof(DefaultMinusPoint);
        }

        public class FCloud
        {
            public const string GroupName = nameof(FCloud);
            public const string SecretId = nameof(SecretId);
            public const string SecretKey = nameof(SecretKey);
            public const string AppId = nameof(AppId);
        }
    }
}
