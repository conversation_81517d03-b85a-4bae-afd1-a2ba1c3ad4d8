﻿using AutoMapper;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Upload;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Exercises
{
    public class AddExerciseUseCase(IUnitOfWork unitOfWork,
                              IMapper mapper,
                              IMediaStorageService mediaStorage) : BaseExerciseUseCase(unitOfWork, mapper, mediaStorage), IAddExerciseUseCase
    {
        public async Task<IApiResponse> AddAsync(AddExerciseInput input)
        {
            var repo = GetGenericRepository<Exercise>();
            var exercise = Map<Exercise>(input);

            if (input.Items != null && input.Items.Any())
            {
                var items = input.Items.ToList();
                var exerciseItems = await CreateExerciseItemsAsync(items);
                exercise.ExerciseItems.AddRange(exerciseItems);
            }

            if (input.Answers != null && input.Answers.Any())
            {
                var answersList = input.Answers.ToList();
                var exerciseAnswers = await CreateAnswersAsync(answersList, input.TypeId);
                exercise.ExerciseAnswers.AddRange(exerciseAnswers);
            }

            await repo.AddAsync(exercise);
            await CommitAsync();
            var output = Mapper.Map<ExerciseModel>(exercise);
            return ApiResponse.Ok(output, MessageDefinition.Succeeded);
        }
    }
}
