﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.Property(x => x.MaxTokenCount)
                   .HasDefaultValue(1);

            builder.HasMany(x => x.RefreshTokens)
                   .WithOne(x => x.User)
                   .HasForeignKey(x => x.UserId)
                   .HasConstraintName("FK_User_RefreshToken");

            builder.HasIndex(x => x.UserName)
                   .HasDatabaseName("Idx_Username");

            builder.HasIndex(x => x.Token)
                   .HasDatabaseName("Idx_Token");

            builder.HasMany(x => x.ApiTokens)
                   .WithOne(x => x.User)
                   .HasForeignKey(x => x.UserId)
                   .HasConstraintName("FK_User_Token");

            builder.HasMany(u => u.SubscriptionUsers)
                   .WithOne(su => su.User)
                   .HasForeignKey(su => su.UserId);

            builder.HasMany(u => u.Progresses)
                   .WithOne(p => p.User)
                   .HasForeignKey(p => p.UserId);

            builder.HasOne(u => u.Class)
                   .WithMany(c => c.Users)
                   .HasForeignKey(u => u.ClassId)
                   .IsRequired(false);

            builder.HasMany(u => u.UserAnswers)
                   .WithOne(ua => ua.User)
                   .HasForeignKey(ua => ua.UserId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull);

            builder.HasMany(u => u.PronunciationResults)
                   .WithOne(ua => ua.User)
                   .HasForeignKey(ua => ua.UserId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
