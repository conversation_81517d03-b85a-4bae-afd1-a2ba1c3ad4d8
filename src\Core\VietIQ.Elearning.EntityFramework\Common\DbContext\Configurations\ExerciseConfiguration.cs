﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class ExerciseConfiguration : IEntityTypeConfiguration<Exercise>
    {
        public void Configure(EntityTypeBuilder<Exercise> builder)
        {
            builder.Has<PERSON>ey(e => e.Id);

            builder.Property(x => x.Code)
                   .IsRequired()
                   .HasMaxLength(20);

            builder.Property(x => x.QuestionData)
                   .IsRequired()
                   .HasMaxLength(2000);

            builder.Property(e => e.Explanation)
                   .HasMaxLength(2000);

            builder.Property(e => e.MetaData)
                .HasColumnType("jsonb");

            builder.HasIndex(e => e.LessonId)
                .HasDatabaseName("IX_Exercise_LessonId");

            builder.HasOne(e => e.Lesson)
                   .WithMany(l => l.Exercises)
                   .HasForeignKey(e => e.LessonId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(e => e.ExerciseType)
                   .WithMany()
                   .HasForeignKey(e => e.TypeId)
                   .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(e => e.ExerciseTags)
                   .WithOne(p => p.Exercise)
                   .HasForeignKey(p => p.ExerciseId);

            builder.HasMany(e => e.ExerciseItems)
                   .WithOne(i => i.Exercise)
                   .HasForeignKey(i => i.ExerciseId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(e => e.UserAnswers)
                   .WithOne(ua => ua.Exercise)
                   .HasForeignKey(ua => ua.ExerciseId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
