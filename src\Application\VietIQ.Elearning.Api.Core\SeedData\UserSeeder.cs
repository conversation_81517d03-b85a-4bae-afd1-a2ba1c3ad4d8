﻿using Microsoft.AspNetCore.Identity;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Shared.SeedData
{
    internal class UserSeeder : BaseDataSeeder
    {
        private readonly string defaultPassword = "Abc@123456";

        public UserSeeder(ApplicationDbContext context, bool isDevelopment)
            : base(context, isDevelopment)
        {
        }

        public void SeedData()
        {
            var users = new List<(string userName, string? firstName, string? lastName, string? address, int? classId)>();
            var passwordHasher = new PasswordHasher<User>();
            var usersToAdd = new List<User>();
            users.Add(new(User.MasterName, "Super", "Admin", "Viet Nam", null));

            if (IsDevelopment)
            {
                users.Add(new("andy", "<PERSON>uy<PERSON> Van", "A", "Ha Noi", null));
                users.Add(new("andy1", "<PERSON><PERSON><PERSON>", "A", "<PERSON> Noi", 1));
            }

            // 1. <PERSON><PERSON><PERSON> danh sách user mới (chưa add ApiToken)
            foreach (var (username, firstName, lastName, address, classId) in users)
            {
                if (this.Context.Users.Any(x => x.UserName == username))
                {
                    continue;
                }

                var user = new User
                {
                    UserName = username,
                    NormalizedUserName = username.ToUpper(),
                    Email = $"{username}@example.com",
                    NormalizedEmail = $"{username.ToUpper()}@EXAMPLE.COM",
                    SecurityStamp = Guid.NewGuid().ToString(),
                    EmailConfirmed = true,
                    IsEnabled = true,
                    FirstName = firstName,
                    LastName = lastName,
                    Address = address,
                    IsDeleted = false,
                    MaxTokenCount = 1,
                    PhoneNumber = "0123456789",
                    ClassId = classId,
                };
                user.PasswordHash = passwordHasher.HashPassword(user, defaultPassword);

                usersToAdd.Add(user);
            }

            this.Context.Users.AddRange(usersToAdd);
            this.Context.SaveChanges();

            var apiTokens = usersToAdd.Select(user => new ApiToken
            {
                UserId = user.Id,
                Token = Guid.NewGuid().ToString("N"),
                IsDefault = true,
                CreatedTime = DateTimeOffset.UtcNow,
            }).ToList();

            this.Context.ApiTokens?.AddRange(apiTokens);
            this.Context.SaveChanges();
        }
    }
}
