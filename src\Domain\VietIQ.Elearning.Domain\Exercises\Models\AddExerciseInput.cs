﻿using VietIQ.Elearning.Domain.ExerciseAnswers;
using VietIQ.Elearning.Domain.ExerciseItems;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Exercises
{
    public class AddExerciseInput
    {
        public int LessonId { get; set; }
        public int GradeId { get; set; }
        public int WeekId { get; set; }
        public string Title { get; set; } = null!;
        public string Code { get; set; } = null!;
        public ExerciseTypeEnum TypeId { get; set; }
        public string QuestionData { get; set; } = null!;
        public string? MetaData { get; set; } = null!;
        public DisplayDirection DisplayDirection { get; set; }
        public string? Explanation { get; set; }

        public IEnumerable<AddExerciseItemInput>? Items { get; set; }
        public IEnumerable<AddExerciseAnswerInput>? Answers { get; set; }
    }

}
