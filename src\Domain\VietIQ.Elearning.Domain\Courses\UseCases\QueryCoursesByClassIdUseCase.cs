﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Courses
{
    public class QueryCoursesByGradeIdUseCase(IUnitOfWork unitOfWork,
                                              IMapper mapper) : BaseService(unitOfWork, mapper), IQueryCoursesByGradeIdUseCase
    {
        public async Task<IApiResponse> ExecuteAsync(int gradeId)
        {
            var repo = GetGenericRepository<Course>();
            var entities = await repo.GetAsync(x => x.GradeId == gradeId);
            if (entities == null || !entities.Any())
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<IEnumerable<CourseModel>>(entities));
        }
    }
}
