﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class ClassConfiguration : IEntityTypeConfiguration<Class>
    {
        public void Configure(EntityTypeBuilder<Class> builder)
        {
            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.HasIndex(c => c.OrganizationId)
                   .HasDatabaseName("IX_Class_OrganizationId");

            builder.HasIndex(c => c.Name)
                   .HasDatabaseName("IX_Class_Name");

            builder.HasMany(i => i.Users)
                   .WithOne(u => u.Class)
                   .HasForeignKey(u => u.ClassId);
        }
    }
}
