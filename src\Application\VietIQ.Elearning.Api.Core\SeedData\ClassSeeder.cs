﻿using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Core.SeedData
{
    internal class ClassSeeder : BaseDataSeeder
    {
        public ClassSeeder(ApplicationDbContext context, bool isDevelopment) : base(context, isDevelopment)
        {
        }

        public void SeedData()
        {
            var dbSet = this.Context.Set<Class>();
            if (dbSet != null && !dbSet.Any() && IsDevelopment)
            {
                dbSet.AddRange(
                    new Class
                    {
                        Id = 1,
                        OrganizationId = 1,
                        Code = "L1A",
                        Name = "Lớp 1A",
                        GradeId = 1,
                        AcademicYear = 2025
                    },
                    new Class
                    {
                        Id = 2,
                        OrganizationId = 1,
                        Code = "L1B",
                        Name = "Lớp 1B",
                        GradeId = 1,
                        AcademicYear = 2025
                    }
                );

                this.Context.SaveChanges();
            }
        }
    }
}
