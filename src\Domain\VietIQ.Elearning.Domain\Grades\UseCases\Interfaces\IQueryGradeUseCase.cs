﻿using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Grades
{
    public interface IQueryGradeUseCase
    {
        Task<IApiResponse> GetAsync(IPaginationInput input);
        Task<IApiResponse> GetByIdAsync(int id);
        Task<IApiResponse> GetCoursesByGradeIdAsync(int gradeId);
        Task<IApiResponse> GetClassesByGradeIdAsync(int gradeId);
    }
}
