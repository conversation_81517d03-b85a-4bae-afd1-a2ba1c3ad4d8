﻿using VietIQ.Elearning.Core.Users;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Users
{
    public interface IQueryExtendedUserUseCase
    {
        Task<IApiResponse> GetExtendedUsersAsync(IPaginationInput paginationInput, CancellationToken cancellationToken = default);

        Task<IApiResponse<ExtendedUserModel>> GetExtendedUserByIdAsync(int id, CancellationToken cancellationToken = default);

        Task<IApiResponse<ExtendedUserModel>> GetExtendedUserByTokenAsync(string token, CancellationToken cancellationToken = default);
    }
}
