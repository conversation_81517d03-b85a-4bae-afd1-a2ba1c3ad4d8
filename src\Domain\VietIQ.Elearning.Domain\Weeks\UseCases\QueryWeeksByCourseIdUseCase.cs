﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Weeks
{
    public class QueryWeeksByCourseIdUseCase(IUnitOfWork unitOfWork,
                                             IMapper mapper) : BaseService(unitOfWork, mapper), IQueryWeeksByCourseIdUseCase
    {
        public async Task<IApiResponse> ExecuteAsync(int courseId)
        {
            var repo = GetGenericRepository<Week>();
            var entities = await repo.GetAsync(x => x.CourseId == courseId);
            if (entities == null || !entities.Any())
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<IEnumerable<WeekModel>>(entities));
        }
    }
}
