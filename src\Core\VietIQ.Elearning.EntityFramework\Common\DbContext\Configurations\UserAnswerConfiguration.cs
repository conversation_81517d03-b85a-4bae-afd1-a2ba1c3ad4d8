﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class UserAnswerConfiguration : IEntityTypeConfiguration<UserAnswer>
    {
        public void Configure(EntityTypeBuilder<UserAnswer> builder)
        {
            builder.HasQueryFilter(a => !a.Exercise.IsDeleted);

            builder.HasOne(x => x.User)
                   .WithMany(x => x.UserAnswers)
                   .HasForeignKey(x => x.UserId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(x => x.Exercise)
                   .WithMany(e => e.UserAnswers)
                   .HasForeignKey(x => x.ExerciseId)
                   .IsRequired(false);

            builder.HasOne(x => x.ExerciseItem)
                   .WithMany()
                   .HasForeignKey(x => x.ExerciseItemId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ua => ua.PronunciationResult)
                   .WithOne(pr => pr.UserAnswer)
                   .HasForeignKey<UserAnswer>(ua => ua.PronunciationResultId)
                   .OnDelete(DeleteBehavior.SetNull);

        }
    }
}
