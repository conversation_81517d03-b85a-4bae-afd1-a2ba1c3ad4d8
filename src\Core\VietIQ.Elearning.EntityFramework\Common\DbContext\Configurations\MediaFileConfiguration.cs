﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class MediaFileConfiguration : IEntityTypeConfiguration<MediaFile>
    {
        public void Configure(EntityTypeBuilder<MediaFile> builder)
        {
            builder.HasKey(m => m.Id);

            builder.Property(m => m.FilePath)
                   .IsRequired()
                   .HasMaxLength(500);

            builder.Property(m => m.FileName)
                   .IsRequired()
                   .HasMaxLength(200);
        }
    }
}
