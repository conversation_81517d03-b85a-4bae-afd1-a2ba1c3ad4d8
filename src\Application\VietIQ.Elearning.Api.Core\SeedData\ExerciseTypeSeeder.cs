﻿using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Core.SeedData
{
    class ExerciseTypeSeeder : BaseDataSeeder
    {
        public ExerciseTypeSeeder(ApplicationDbContext context, bool isDevelopment) : base(context, isDevelopment)
        {
        }
        public void SeedData()
        {
            var dbSet = this.Context.Set<ExerciseType>();
            if (dbSet != null && !dbSet.Any())
            {
                dbSet.AddRange(
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.AudioChoice,
                        Code = "audio_choice",
                        Name = ExerciseTypeEnum.AudioChoice.ToString(),
                        ScoringRules = "{}"
                    }, // Nghe → chọn đáp án
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.TextInput,
                        Name = ExerciseTypeEnum.TextInput.ToString(),
                        Code = "text_input",
                        ScoringRules = "{}"
                    }, // Chọn đáp án từ text
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.Speaking,
                        Name = ExerciseTypeEnum.Speaking.ToString(),
                        Code = "speaking",
                        ScoringRules = "{}"
                    }, // Ghi âm trả lời
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.ImageChoice,
                        Name = ExerciseTypeEnum.ImageChoice.ToString(),
                        Code = "image_choice",
                        ScoringRules = "{}"
                    }, // Chọn đáp án từ ảnh
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.Translation,
                        Name = ExerciseTypeEnum.Translation.ToString(),
                        Code = "translation",
                        ScoringRules = "{}"
                    }, // Dịch
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.Writing,
                        Name = ExerciseTypeEnum.Writing.ToString(),
                        Code = "writing",
                        ScoringRules = "{}"
                    }, // Nhập đoạn văn
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.AudioFill,
                        Name = ExerciseTypeEnum.AudioFill.ToString(),
                        Code = "audio_fill",
                        ScoringRules = "{}"
                    }, // Nghe → điền
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.Pronunciation,
                        Name = ExerciseTypeEnum.Pronunciation.ToString(),
                        Code = "pronunciation",
                        ScoringRules = "{}"
                    }, // Phát âm (ghi âm đầu ra)
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.SentenceOrdering,
                        Name = ExerciseTypeEnum.SentenceOrdering.ToString(),
                        Code = "sentence_ordering",
                        ScoringRules = "{}"
                    }, // Sắp xếp câu
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.Matching,
                        Name = ExerciseTypeEnum.Matching.ToString(),
                        Code = "matching",
                        ScoringRules = "{}"
                    }, // Nối cặp
                    new ExerciseType
                    {
                        Id = (int)ExerciseTypeEnum.TextChoice,
                        Name = ExerciseTypeEnum.TextChoice.ToString(),
                        Code = "text_choice",
                        ScoringRules = "{}"
                    } // Chọn đáp án từ text
                );

                this.Context.SaveChanges();
            }
        }
    }
}
