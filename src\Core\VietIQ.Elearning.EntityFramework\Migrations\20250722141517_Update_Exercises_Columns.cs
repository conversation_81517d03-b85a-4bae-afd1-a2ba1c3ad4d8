﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace VietIQ.Elearning.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class Update_Exercises_Columns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "ExerciseTypes",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CorrectOrder",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Explanation",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FillBlankTemplate",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MatchPairs",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "QuestionContent",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GroupKey",
                table: "ExerciseAnswers",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                table: "ExerciseTypes");

            migrationBuilder.DropColumn(
                name: "CorrectOrder",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "Explanation",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "FillBlankTemplate",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "MatchPairs",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "QuestionContent",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "GroupKey",
                table: "ExerciseAnswers");
        }
    }
}
