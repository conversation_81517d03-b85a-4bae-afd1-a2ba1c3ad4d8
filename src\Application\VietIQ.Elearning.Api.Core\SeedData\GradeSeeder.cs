﻿using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Core.SeedData
{
    internal class GradeSeeder : BaseDataSeeder
    {
        public GradeSeeder(ApplicationDbContext context, bool isDevelopment) : base(context, isDevelopment)
        {
        }

        public void SeedData()
        {
            var dbSet = this.Context.Set<Grade>();
            if (dbSet != null && !dbSet.Any() && IsDevelopment)
            {
                dbSet.AddRange(
                    new Grade
                    {
                        Id = 1,
                        Code = "LH1",
                        Name = "English Beginner A",
                        Description = "This is a beginner level English class for students who are just starting to learn the language."
                    },
                    new Grade
                    {
                        Id = 2,
                        Code = "LH2",
                        Name = "English Intermediate B",
                        Description = "This class is designed for students who have a basic understanding of English and want to improve their skills."
                    }
                );

                this.Context.SaveChanges();
            }
        }
    }
}
