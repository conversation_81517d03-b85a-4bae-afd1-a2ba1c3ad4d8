<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>0ff8bee6-3029-4a0c-bccc-82a2eec8d40e</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Domain\VietIQ.Elearning.JobAsync\VietIQ.Elearning.JobAsync.csproj" />
    <ProjectReference Include="..\VietIQ.Elearning.Api.Core\VietIQ.Elearning.Api.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="storage\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Asset\Templates\confirm-email.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Asset\Templates\information-email.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
