﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class ExerciseAnswerConfiguration : IEntityTypeConfiguration<ExerciseAnswer>
    {
        public void Configure(EntityTypeBuilder<ExerciseAnswer> builder)
        {
            builder.HasQueryFilter(a => !a.Exercise.IsDeleted);

            builder.<PERSON><PERSON>ey(x => x.Id);

            builder.Property(a => a.MediaType)
                   .HasConversion<int>();

            builder.HasIndex(x => x.ExerciseId);

            builder.Property(a => a.Explanation)
                   .HasMaxLength(1000);

            builder.HasOne(a => a.Exercise)
                   .WithMany(e => e.ExerciseAnswers)
                   .HasForeignKey(a => a.ExerciseId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(a => a.MediaFile)
                   .WithMany()
                   .HasForeignKey(a => a.MediaFileId)
                   .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
