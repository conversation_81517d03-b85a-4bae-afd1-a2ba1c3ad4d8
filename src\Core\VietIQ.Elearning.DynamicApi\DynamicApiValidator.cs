﻿using Microsoft.Extensions.DependencyInjection;

namespace VietIQ.Elearning.DynamicApi
{
    public static class DynamicApiValidator
    {
        public static void Validate(IServiceCollection services, bool throwOnViolation = true)
        {
            var allTypes = AppDomain.CurrentDomain
                .GetAssemblies()
                .Where(a => !a.IsDynamic
                            && !string.IsNullOrEmpty(a.FullName)
                            && !a.FullName.StartsWith("Microsoft")
                            && !a.FullName.StartsWith("System"))
                .SelectMany(a => a.GetTypes())
                .Where(t => t.IsClass && !t.IsAbstract)
                .ToList();

            var appServices = allTypes
                .Where(t => typeof(IDynamicApi).IsAssignableFrom(t) && t.Name.EndsWith("AppService"))
                .ToList();

            var violations = new System.Text.StringBuilder();

            foreach (var appService in appServices)
            {
                var constructor = appService.GetConstructors().FirstOrDefault();
                if (constructor == null) continue;

                foreach (var parameter in constructor.GetParameters())
                {
                    var paramType = parameter.ParameterType;

                    // Vi phạm: UseCase cũng là IDynamicApi
                    if (typeof(IDynamicApi).IsAssignableFrom(paramType) && paramType.Name.EndsWith("UseCase"))
                    {
                        violations.AppendLine($"""
                        ❌ Vi phạm kiến trúc:
                           - `{appService.Name}` kế thừa IDynamicApi và inject `{paramType.Name}`,
                           - `{paramType.Name}` cũng kế thừa IDynamicApi.
                           ⇒ Hãy loại bỏ IDynamicApi khỏi UseCase hoặc không expose AppService.
                        """);
                    }
                }
            }

            if (violations.Length > 0)
            {
                var message = violations.ToString();

                if (throwOnViolation)
                {
                    throw new InvalidOperationException(message);
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine("Cảnh báo vi phạm quy ước kiến trúc:");
                    Console.WriteLine(message);
                    Console.ResetColor();
                }
            }
        }
    }
}
