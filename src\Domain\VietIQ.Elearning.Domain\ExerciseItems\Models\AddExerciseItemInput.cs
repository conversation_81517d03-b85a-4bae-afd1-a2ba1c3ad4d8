﻿using Microsoft.AspNetCore.Http;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.ExerciseItems
{
    public class AddExerciseItemInput : IExerciseItemInput
    {
        public DisplayTypeEnum DisplayType { get; set; }
        public string? QuestionContent { get; set; } = null!;
        public int Order { get; set; }
        public string? ExpectedAnswerText { get; set; }
        public string? SentenceOrderingJson { get; set; }
        public List<MatchingPairInput>? MatchingPairs { get; set; }

        public IFormFile? File { get; set; }

    }
}
