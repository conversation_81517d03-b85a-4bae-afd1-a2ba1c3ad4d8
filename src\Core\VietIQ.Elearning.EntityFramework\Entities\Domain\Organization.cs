﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Organizations")]
    public class Organization : AuditEntity<int>
    {
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string Type { get; set; } = null!;
        public StatusType Status { get; set; }

        public virtual List<Class> Classes { get; set; } = [];
        public virtual List<Subscription> Subscriptions { get; set; } = [];
    }
}
