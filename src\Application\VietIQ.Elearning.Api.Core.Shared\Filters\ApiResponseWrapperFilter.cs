﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using VietIQ.Elearning.DynamicApi;
using VietIQ.Elearning.DynamicApi.Response;

namespace VietIQ.Elearning.Api.Shared.Filters
{
    public class ApiResponseWrapperFilter : IResultFilter
    {
        public void OnResultExecuting(ResultExecutingContext context)
        {
            var hasNonFormat = context.ActionDescriptor.EndpointMetadata
                .OfType<NonFormatResultAttribute>()
                .Any();

            if (hasNonFormat)
                return;

            if (context.Result is FileResult or FileStreamResult or PhysicalFileResult or VirtualFileResult)
                return;

            if (context.Result is ObjectResult objectResult)
            {
                if (objectResult.Value is IApiResponse)
                    return;

                context.Result = new ObjectResult(ApiResponse.Ok(objectResult.Value))
                {
                    StatusCode = objectResult.StatusCode
                };
            }

            else if (context.Result is ContentResult contentResult)
            {
                context.Result = new ObjectResult(ApiResponse.Ok(contentResult.Content, 200))
                {
                    StatusCode = contentResult.StatusCode
                };
            }

            else if (context.Result is EmptyResult)
            {
                context.Result = new ObjectResult(ApiResponse.Ok())
                {
                    StatusCode = 204
                };
            }

            else if (context.Result is JsonResult jsonResult)
            {
                if (jsonResult.Value is not IApiResponse)
                {
                    context.Result = new ObjectResult(ApiResponse.Ok(jsonResult.Value))
                    {
                        StatusCode = jsonResult.StatusCode
                    };
                }
            }
        }

        public void OnResultExecuted(ResultExecutedContext context)
        {
            // No post-processing needed
        }
    }
}
