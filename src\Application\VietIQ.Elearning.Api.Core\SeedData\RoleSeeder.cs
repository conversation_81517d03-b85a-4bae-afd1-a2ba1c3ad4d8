﻿using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Shared.SeedData
{
    internal class RoleSeeder : BaseDataSeeder
    {
        public RoleSeeder(ApplicationDbContext context, bool isDevelopment)
            : base(context, isDevelopment)
        {
        }

        public void SeedData()
        {
            AddNew(Role.MasterName, "Administrator");
            Add<PERSON>ew("teacher", "Teacher", false);
            Add<PERSON>ew("user", "User", true);

            if (IsDevelopment)
            {
                AddNew("agency", "Agency", false);
            }

            this.Context.SaveChanges();
        }

        private void AddNew(string name, string description, bool isDefault = false)
        {
            var role = new Role()
            {
                Name = name,
                NormalizedName = name.ToUpper(),
                Description = description,
                IsDefault = isDefault,
            };

            if (!this.Any<Role>(p => p.Name == name))
            {
                this.Context.Roles.Add(role);
            }
        }
    }
}
