﻿namespace VietIQ.Elearning.Api.Shared.Permissions
{
    public class PermissionDefinitionProvider : BasePermissionDefinitionProvider
    {
        public PermissionDefinitionProvider(IPermissionContext context) : base(context)
        {
        }

        public override void Define(IPermissionContext context)
        {
            // Dash board
            var dashboard = context.AddPermission(PermissionNames.Dashboard.View);
            dashboard.AddPermission(PermissionNames.Dashboard.Add);
            dashboard.AddPermission(PermissionNames.Dashboard.Update);
            dashboard.AddPermission(PermissionNames.Dashboard.Delete);

            // Other user permission: Xem và cập nhật lại permissions cho một user khác
            var otherUserPermission = context.AddPermission(PermissionNames.OtherUserPermission.View);
            otherUserPermission.AddPermission(PermissionNames.OtherUserPermission.Update);

            // Role: Xem và cập nhật lại permissions cho role
            var rolePermission = context.AddPermission(PermissionNames.RolePermission.View);
            rolePermission.AddPermission(PermissionNames.RolePermission.Update);

            // User: Th<PERSON><PERSON>, sửa, xóa user
            var user = context.AddPermission(PermissionNames.User.View);
            user.AddPermission(PermissionNames.User.Add);
            user.AddPermission(PermissionNames.User.Update);
            user.AddPermission(PermissionNames.User.Delete);

            // Role: Thêm sửa xóa role
            var role = context.AddPermission(PermissionNames.Role.View);
            role.AddPermission(PermissionNames.Role.Add);
            role.AddPermission(PermissionNames.Role.Update);
            role.AddPermission(PermissionNames.Role.Delete);

            // Setting : Các quyền liên quan tới chỉnh sửa giá trị setting hệ thống
            var setting = context.AddPermission(PermissionNames.Setting.View);
            setting.AddPermission(PermissionNames.Setting.BasicEdit);
            setting.AddPermission(PermissionNames.Setting.SmtpEdit);
            setting.AddPermission(PermissionNames.Setting.ConfirmAccountEdit);
            setting.AddPermission(PermissionNames.Setting.AuthenticationEdit);

            // ApiToken : Thêm sửa xóa ApiToken
            var apiToken = context.AddPermission(PermissionNames.ApiToken.View);
            apiToken.AddPermission(PermissionNames.ApiToken.Add);
            apiToken.AddPermission(PermissionNames.ApiToken.Update);
            apiToken.AddPermission(PermissionNames.ApiToken.Delete);

            // MaxApiTokenThreshold : Xem và cập nhật lại MaxApiTokenThreshold
            var maxApiTokenThreshold = context.AddPermission(PermissionNames.MaxApiTokenThreshold.View);
            maxApiTokenThreshold.AddPermission(PermissionNames.MaxApiTokenThreshold.Update);

            // Session
            var session = context.AddPermission(PermissionNames.Session.View);
            session.AddPermission(PermissionNames.Session.Add);
            session.AddPermission(PermissionNames.Session.Update);
            session.AddPermission(PermissionNames.Session.Delete);

            //Notification
            var notification = context.AddPermission(PermissionNames.Notification.View);
            notification.AddPermission(PermissionNames.Notification.Add);
            notification.AddPermission(PermissionNames.Notification.Marked);
            notification.AddPermission(PermissionNames.Notification.Delete);

            //Week
            var week = context.AddPermission(PermissionNames.Week.View);
            week.AddPermission(PermissionNames.Week.Add);
            week.AddPermission(PermissionNames.Week.Update);
            week.AddPermission(PermissionNames.Week.Delete);

            // Lesson
            var lesson = context.AddPermission(PermissionNames.Lesson.View);
            lesson.AddPermission(PermissionNames.Lesson.Add);
            lesson.AddPermission(PermissionNames.Lesson.Update);
            lesson.AddPermission(PermissionNames.Lesson.Delete);

            // Organization
            var organization = context.AddPermission(PermissionNames.Organization.View);
            organization.AddPermission(PermissionNames.Organization.Add);
            organization.AddPermission(PermissionNames.Organization.Update);
            organization.AddPermission(PermissionNames.Organization.Delete);

            // Class
            var classPermission = context.AddPermission(PermissionNames.Class.View);
            classPermission.AddPermission(PermissionNames.Class.Add);
            classPermission.AddPermission(PermissionNames.Class.Update);
            classPermission.AddPermission(PermissionNames.Class.Delete);

            // Course
            var course = context.AddPermission(PermissionNames.Course.View);
            course.AddPermission(PermissionNames.Course.Add);
            course.AddPermission(PermissionNames.Course.Update);
            course.AddPermission(PermissionNames.Course.Delete);

            // Exercise
            var exercise = context.AddPermission(PermissionNames.Exercise.View);
            exercise.AddPermission(PermissionNames.Exercise.Add);
            exercise.AddPermission(PermissionNames.Exercise.Update);
            exercise.AddPermission(PermissionNames.Exercise.Delete);

            // Grade
            var grade = context.AddPermission(PermissionNames.Grade.View);
            grade.AddPermission(PermissionNames.Grade.Add);
            grade.AddPermission(PermissionNames.Grade.Update);
            grade.AddPermission(PermissionNames.Grade.Delete);
        }
    }
}