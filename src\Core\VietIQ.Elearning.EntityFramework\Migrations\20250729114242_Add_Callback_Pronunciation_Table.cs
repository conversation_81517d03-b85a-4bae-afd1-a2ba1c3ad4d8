﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace VietIQ.Elearning.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class Add_Callback_Pronunciation_Table : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserAnswers_Exercises_ExerciseId",
                table: "UserAnswers");

            migrationBuilder.AddColumn<bool>(
                name: "IsSkipped",
                table: "UserAnswers",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "PronunciationResultId",
                table: "UserAnswers",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PronunciationResults",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RequestId = table.Column<string>(type: "text", nullable: false),
                    ExerciseId = table.Column<int>(type: "integer", nullable: true),
                    ExerciseItemId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    Score = table.Column<int>(type: "integer", nullable: true),
                    Feedback = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    Status = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    ErrorCode = table.Column<int>(type: "integer", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    AudioFileUrl = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    ProcessingTime = table.Column<double>(type: "double precision", nullable: true),
                    WordsPerMin = table.Column<double>(type: "double precision", nullable: true),
                    SpeakingDuration = table.Column<double>(type: "double precision", nullable: true),
                    UserPhoneme = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    WordsScoreDetail = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    PhonemeScoreStatistics = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    StressResponse = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PronunciationResults", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PronunciationResults_Core_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Core_Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PronunciationResults_ExerciseItems_ExerciseItemId",
                        column: x => x.ExerciseItemId,
                        principalTable: "ExerciseItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PronunciationResults_Exercises_ExerciseId",
                        column: x => x.ExerciseId,
                        principalTable: "Exercises",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserAnswers_PronunciationResultId",
                table: "UserAnswers",
                column: "PronunciationResultId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PronunciationResults_ExerciseId",
                table: "PronunciationResults",
                column: "ExerciseId");

            migrationBuilder.CreateIndex(
                name: "IX_PronunciationResults_ExerciseItemId",
                table: "PronunciationResults",
                column: "ExerciseItemId");

            migrationBuilder.CreateIndex(
                name: "IX_PronunciationResults_UserId",
                table: "PronunciationResults",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserAnswers_Exercises_ExerciseId",
                table: "UserAnswers",
                column: "ExerciseId",
                principalTable: "Exercises",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_UserAnswers_PronunciationResults_PronunciationResultId",
                table: "UserAnswers",
                column: "PronunciationResultId",
                principalTable: "PronunciationResults",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserAnswers_Exercises_ExerciseId",
                table: "UserAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_UserAnswers_PronunciationResults_PronunciationResultId",
                table: "UserAnswers");

            migrationBuilder.DropTable(
                name: "PronunciationResults");

            migrationBuilder.DropIndex(
                name: "IX_UserAnswers_PronunciationResultId",
                table: "UserAnswers");

            migrationBuilder.DropColumn(
                name: "IsSkipped",
                table: "UserAnswers");

            migrationBuilder.DropColumn(
                name: "PronunciationResultId",
                table: "UserAnswers");

            migrationBuilder.AddForeignKey(
                name: "FK_UserAnswers_Exercises_ExerciseId",
                table: "UserAnswers",
                column: "ExerciseId",
                principalTable: "Exercises",
                principalColumn: "Id");
        }
    }
}
