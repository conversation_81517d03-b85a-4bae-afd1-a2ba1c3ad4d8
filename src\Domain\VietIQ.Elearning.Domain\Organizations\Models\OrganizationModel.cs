﻿using VietIQ.Elearning.Core.Users;
using VietIQ.Elearning.Domain.Classes;
using VietIQ.Elearning.Domain.Subscriptions;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Organizations
{
    public class OrganizationModel
    {
        public int Id { get; set; }

        public string Code { get; set; } = null!;

        public string Name { get; set; } = null!;

        public string? Type { get; set; }

        public StatusType Status { get; set; }

        public IEnumerable<ClassModel>? Classes { get; set; } = null!;

        public IEnumerable<UserModel>? Users { get; set; } = null!;

        public IEnumerable<SubscriptionModel>? Subscriptions { get; set; }
    }
}