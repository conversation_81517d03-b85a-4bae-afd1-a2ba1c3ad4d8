﻿using System.ComponentModel.DataAnnotations.Schema;
using VietIQ.Elearning.EntityFramework.Entities.Domain;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("UserAnswers")]
    public class UserAnswer : AuditEntity<int>
    {
        public int? UserId { get; set; }
        public int ExerciseId { get; set; }
        public int? AnswerId { get; set; }
        public int ExerciseItemId { get; set; }
        public string UserResponse { get; set; } = null!;
        public bool IsCorrect { get; set; }
        public bool IsSkipped { get; set; }
        public int? PronunciationResultId { get; set; }
        public DateTime ResponseTime { get; set; }

        public virtual User? User { get; set; } = null!;
        public virtual Exercise Exercise { get; set; } = null!;
        public virtual ExerciseItem ExerciseItem { get; set; } = null!;
        public virtual ExerciseAnswer? Answer { get; set; }
        public virtual PronunciationResult? PronunciationResult { get; set; }

    }
}
