﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.Lessons;
using VietIQ.Elearning.Domain.Weeks;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers
{
    [ApiController]
    [Route("api/weeks")]
    [FeatureAuthorize]
    public class WeekController : ApiBaseController
    {
        private readonly IAddWeekUseCase addUseCase;
        private readonly IUpdateWeekUseCase updateUseCase;
        private readonly IDeleteWeekUseCase deleteUseCase;
        private readonly IQueryWeekUseCase queryUseCase;
        private readonly IQueryLessonsByWeekIdUseCase queryLessonsByWeekIdUseCase;

        public WeekController(
            IAddWeekUseCase addUseCase,
            IUpdateWeekUseCase updateUseCase,
            IDeleteWeekUseCase deleteUseCase,
            IQueryWeekUseCase queryUseCase,
            IQueryLessonsByWeekIdUseCase queryLessonsByWeekIdUseCase)
        {
            this.addUseCase = addUseCase;
            this.updateUseCase = updateUseCase;
            this.deleteUseCase = deleteUseCase;
            this.queryUseCase = queryUseCase;
            this.queryLessonsByWeekIdUseCase = queryLessonsByWeekIdUseCase;
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Week.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var response = await queryUseCase.GetAsync(input);
            return response.ToActionResult();
        }


        [HttpGet("all")]
        [FeatureAuthorize(PermissionNames.Week.View)]
        public async Task<IActionResult> GetAllAsync()
        {
            var response = await queryUseCase.GetAllAsync();
            return response.ToActionResult();
        }

        [HttpGet("{weekId}")]
        [FeatureAuthorize(PermissionNames.Week.View)]
        public async Task<IActionResult> GetByIdAsync(int weekId)
        {
            var response = await queryUseCase.GetByIdAsync(weekId);
            return response.ToActionResult();
        }

        [HttpGet("{weekId}/lessons")]
        [FeatureAuthorize(PermissionNames.Week.View)]
        public async Task<IActionResult> GetLessonsByWeekIdAsync(int weekId)
        {
            var response = await queryLessonsByWeekIdUseCase.ExecuteAsync(weekId);
            return response.ToActionResult();
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Week.Add)]
        public async Task<IActionResult> AddAsync([FromBody] AddWeekInput input)
        {
            var response = await addUseCase.AddAsync(input);
            return response.ToActionResult();
        }

        [HttpPut("{id}")]
        [FeatureAuthorize(PermissionNames.Week.Update)]
        public async Task<IActionResult> UpdateAsync(int id, [FromBody] UpdateWeekInput input)
        {
            var response = await updateUseCase.UpdateAsync(id, input);
            return response.ToActionResult();
        }

        [HttpDelete("{id}")]
        [FeatureAuthorize(PermissionNames.Week.Delete)]
        public async Task<IActionResult> DeleteAsync(int id)
        {
            var response = await deleteUseCase.DeleteAsync(id);
            return response.ToActionResult();
        }
    }
}
