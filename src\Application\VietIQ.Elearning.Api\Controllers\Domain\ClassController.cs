﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.Classes;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers.Domain
{
    [ApiController]
    [Route("api/classes")]
    [FeatureAuthorize]
    public class ClassController : ApiBaseController
    {
        private readonly IAddClassUseCase addClassUseCase;
        private readonly IQueryClassUseCase queryClassUseCase;
        private readonly IUpdateClassUseCase updateClassUseCase;
        private readonly IDeleteClassUseCase deleteClassUseCase;

        public ClassController(IAddClassUseCase addClassUseCase,
                               IQueryClassUseCase queryClassUseCase,
                               IUpdateClassUseCase updateClassUseCase,
                               IDeleteClassUseCase deleteClassUseCase)
        {
            this.addClassUseCase = addClassUseCase;
            this.queryClassUseCase = queryClassUseCase;
            this.updateClassUseCase = updateClassUseCase;
            this.deleteClassUseCase = deleteClassUseCase;
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Class.Add)]
        public async Task<IActionResult> AddAsync([FromBody] AddClassInput input)
        {
            var result = await addClassUseCase.AddAsync(input);
            return result.ToActionResult();
        }

        [HttpPut("{classId}")]
        [FeatureAuthorize(PermissionNames.Class.Update)]
        public async Task<IActionResult> UpdateAsync(int classId, [FromBody] UpdateClassInput input)
        {
            var result = await updateClassUseCase.UpdateAsync(classId, input);
            return result.ToActionResult();
        }

        [HttpDelete("{classId}")]
        [FeatureAuthorize(PermissionNames.Class.Delete)]
        public async Task<IActionResult> DeleteAsync(int classId)
        {
            var result = await deleteClassUseCase.DeleteAsync(classId);
            return result.ToActionResult();
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Class.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var result = await queryClassUseCase.GetAsync(input);
            return result.ToActionResult();
        }

        [HttpGet("{classId}")]
        [FeatureAuthorize(PermissionNames.Class.View)]
        public async Task<IActionResult> GetByClassIdAsync(int classId)
        {
            var result = await queryClassUseCase.GetByIdAsync(classId);
            return result.ToActionResult();
        }

        [HttpGet("{classId}/users")]
        [FeatureAuthorize(PermissionNames.Class.View)]
        public async Task<IActionResult> GetUsersByClassIdAsync(int classId)
        {
            var result = await queryClassUseCase.GetUsersByClassIdAsync(classId);
            return result.ToActionResult();
        }
    }
}
