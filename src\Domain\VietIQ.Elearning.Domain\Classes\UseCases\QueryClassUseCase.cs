﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.Core.Users;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Classes
{
    public class QueryClassUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IQueryClassUseCase
    {
        public async Task<IApiResponse> GetAsync(IPaginationInput input)
        {
            var repo = GetGenericRepository<Class>();
            var predicate = PredicateBuilder.False<Class>();
            if (input.HasSearchTerm)
            {
                predicate = predicate.Or(x => EF.Functions.Like(x.Name, input.SearchTermPattern));
            }

            var result = await repo.AsQueryable()
                                   .WhereIf(input.HasSearchTerm, predicate)
                                   .Include(o => o.Organization)
                                   .Include(g => g.Grade)
                                   .ToPaginationAsync(input);
            return MapPagination<Class, ClassModel>(result).ToApiResponse();
        }

        public async Task<IApiResponse> GetByIdAsync(int id)
        {
            var repo = GetGenericRepository<Class, int>();
            var entity = await repo.FindAsync(x => x.Id == id);
            if (entity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Class.ClassNotFound);
            }

            return ApiResponse.Ok(Mapper.Map<ClassModel>(entity));
        }

        public async Task<IApiResponse> GetUsersByClassIdAsync(int classId)
        {
            var classRepo = GetGenericRepository<Class>();
            var userRepo = GetGenericRepository<User>();
            var classEntity = await classRepo.FindAsync(x => x.Id == classId);
            if (classEntity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Class.ClassNotFound);
            }

            var users = await userRepo.AsQueryable().Where(x => x.ClassId == classId).ToListAsync();
            return ApiResponse.Ok(Mapper.Map<IEnumerator<UserModel>>(users));
        }
    }
}
