﻿using Microsoft.Extensions.Configuration;
using System.Text;
using System.Text.RegularExpressions;

namespace VietIQ.Elearning.JwtBearer
{
    internal static class OptionConfig
    {
        public static JwtOption GetJwtOptions(this IConfiguration configuration)
        {
            var jwtOptions = configuration.GetSection("JwtOption").Get<JwtOption>();
            if (jwtOptions == null)
            {
                throw new InvalidOperationException("JwtOption configuration section is missing or invalid.");
            }

            // Get secret from environment variable first, fallback to config
            var secretKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY") ?? jwtOptions.SecretKey;

            if (string.IsNullOrWhiteSpace(secretKey))
            {
                throw new InvalidOperationException("JWT SecretKey must be configured via JWT_SECRET_KEY environment variable");
            }

            // Validate minimum key length (256 bits = 32 bytes)
            var keyBytes = Encoding.UTF8.GetBytes(secretKey);
            if (keyBytes.Length < 32)
            {
                throw new InvalidOperationException("JWT SecretKey must be at least 256 bits (32 characters) long");
            }

            // Validate key entropy (prevent weak keys)
            if (IsWeakKey(secretKey))
            {
                throw new InvalidOperationException("JWT SecretKey appears to be weak. Use a cryptographically strong key");
            }

            jwtOptions.SecretKey = secretKey;
            return jwtOptions;
        }

        private static bool IsWeakKey(string key)
        {
            // Check for repeated patterns
            if (Regex.IsMatch(key, @"(.)\1{5,}")) return true;

            // Check for common patterns
            var weakPatterns = new[] { "hello", "test", "secret", "password", "123456" };
            return weakPatterns.Any(pattern => key.ToLowerInvariant().Contains(pattern));
        }
    }
}
