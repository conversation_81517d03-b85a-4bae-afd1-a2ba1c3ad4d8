﻿using AutoMapper;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Exercises
{
    public class ExerciseMappingProfile : Profile
    {
        public ExerciseMappingProfile()
        {
            CreateMap<Exercise, ExerciseModel>().ReverseMap();
            CreateMap<AddExerciseInput, Exercise>().ForMember(dest => dest.Id, opt => opt.Ignore());
            CreateMap<UpdateExerciseInput, Exercise>();
        }
    }
}
