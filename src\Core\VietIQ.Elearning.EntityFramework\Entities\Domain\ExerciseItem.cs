﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("ExerciseItems")]
    public class ExerciseItem : Entity<int>
    {
        public int ExerciseId { get; set; }

        public MediaType MediaType { get; set; } = MediaType.Text;

        public Guid? MediaFileId { get; set; }

        public string? Content { get; set; }

        public int DisplayOrder { get; set; }

        public virtual Exercise Exercise { get; set; } = null!;

        public virtual MediaFile? MediaFile { get; set; }
    }
}
