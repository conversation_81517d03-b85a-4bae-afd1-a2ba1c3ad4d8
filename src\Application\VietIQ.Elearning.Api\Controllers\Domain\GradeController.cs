﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.Grades;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Api.Controllers.Domain
{
    [ApiController]
    [Route("api/grades")]
    [FeatureAuthorize]
    public class GradeController : ApiBaseController
    {
        private readonly IAddGradeUseCase addGradeUseCase;
        private readonly IQueryGradeUseCase queryGradeUseCase;
        private readonly IQueryAllGradeUseCase queryAllGradeUse;
        private readonly IUpdateGradeUseCase updateGradeUseCase;
        private readonly IDeleteGradeUseCase deleteGradeUseCase;

        public GradeController(IAddGradeUseCase addGradeUseCase,
                               IQueryGradeUseCase queryGradeUseCase,
                               IUpdateGradeUseCase updateGradeUseCase,
                               IDeleteGradeUseCase deleteGradeUseCase,
                               IQueryAllGradeUseCase queryAllGradeUse)
        {
            this.addGradeUseCase = addGradeUseCase;
            this.queryGradeUseCase = queryGradeUseCase;
            this.updateGradeUseCase = updateGradeUseCase;
            this.deleteGradeUseCase = deleteGradeUseCase;
            this.queryAllGradeUse = queryAllGradeUse;
        }

        [HttpPost]
        [FeatureAuthorize(PermissionNames.Grade.Add)]
        public async Task<IActionResult> AddAsync([FromBody] AddGradeInput input)
        {
            var result = await addGradeUseCase.AddAsync(input);
            return result.ToActionResult();
        }

        [HttpPut("{gradeId}")]
        [FeatureAuthorize(PermissionNames.Grade.Update)]
        public async Task<IActionResult> UpdateAsync(int gradeId, [FromBody] UpdateGradeInput input)
        {
            var result = await updateGradeUseCase.UpdateAsync(gradeId, input);
            return result.ToActionResult();
        }

        [HttpDelete("{gradeId}")]
        [FeatureAuthorize(PermissionNames.Grade.Delete)]
        public async Task<IActionResult> DeleteAsync(int gradeId)
        {
            var result = await deleteGradeUseCase.DeleteAsync(gradeId);
            return result.ToActionResult();
        }

        [HttpGet]
        [FeatureAuthorize(PermissionNames.Grade.View)]
        public async Task<IActionResult> GetAsync([FromQuery] PaginationInput input)
        {
            var result = await queryGradeUseCase.GetAsync(input);
            return result.ToActionResult();
        }

        [HttpGet("all")]
        [FeatureAuthorize(PermissionNames.Grade.View)]
        public async Task<IActionResult> GetAllGradeAsync()
        {
            var result = await queryAllGradeUse.ExecuteAsync();
            return result.ToActionResult();
        }

        [HttpGet("{gradeId}")]
        [FeatureAuthorize(PermissionNames.Grade.View)]
        public async Task<IActionResult> GetBygradeIdAsync(int gradeId)
        {
            var result = await queryGradeUseCase.GetByIdAsync(gradeId);
            return result.ToActionResult();
        }

        [HttpGet("{gradeId}/courses")]
        [FeatureAuthorize(PermissionNames.Grade.View)]
        public async Task<IActionResult> GetCoursesBygradeIdAsync(int gradeId)
        {
            var result = await queryGradeUseCase.GetByIdAsync(gradeId);
            return result.ToActionResult();
        }

        [HttpGet("{gradeId}/classes")]
        [FeatureAuthorize(PermissionNames.Grade.View)]
        public async Task<IActionResult> GetClassesByGradeIdAsync(int gradeId)
        {
            var result = await queryGradeUseCase.GetClassesByGradeIdAsync(gradeId);
            return result.ToActionResult();
        }
    }
}
