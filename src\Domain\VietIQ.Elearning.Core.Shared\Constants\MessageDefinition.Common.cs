﻿using VietIQ.Elearning.DynamicApi.Response;

namespace VietIQ.Elearning.Core.Constants
{
    public static partial class MessageDefinition
    {
        public static readonly ApiMessage Succeeded = ApiMessage.Create(100_001, "Succeeded");

        public static readonly ApiMessage Failed = ApiMessage.Create(100_002, "Failed");

        public static readonly ApiMessage NotFound = ApiMessage.Create(100_003, "Not found");

        public static readonly ApiMessage InvalidInputParam = ApiMessage.Create(100_004, "Invalid input parameters");

        public static readonly ApiMessage Existed = ApiMessage.Create(100_005, "Existed");

        public static readonly ApiMessage AddFailed = ApiMessage.Create(100_006, "Add failed");

        public static readonly ApiMessage UpdateFailed = ApiMessage.Create(100_007, "Update failed");

        public static readonly ApiMessage DeleteFailed = ApiMessage.Create(100_008, "Delete failed");

        public static readonly ApiMessage NotAllowed = ApiMessage.Create(100_009, "Access denied");

        public static readonly ApiMessage NotFoundOrDisabled = ApiMessage.Create(100_010, "Not found or disabled");

        public static readonly ApiMessage InternalServerError = ApiMessage.Create(100_500, "An occurred error");

        /// <summary>
        /// Code: 101x
        /// </summary>
        public class User
        {
            public static readonly ApiMessage DefaultError = ApiMessage.Create(101_001, "An unknown failure has occurred");

            public static readonly ApiMessage ConcurrencyFailure = ApiMessage.Create(101_002, "Optimistic concurrency failure, object has been modified");

            public static readonly ApiMessage PasswordMismatch = ApiMessage.Create(101_003, "Incorrect password");

            public static readonly ApiMessage InvalidToken = ApiMessage.Create(101_004, "Invalid token");

            public static readonly ApiMessage RecoveryCodeRedemptionFailed = ApiMessage.Create(101_005, "Recovery code redemption failed");

            public static readonly ApiMessage LoginAlreadyAssociated = ApiMessage.Create(101_006, "A user with this login already exists");

            public static readonly ApiMessage InvalidUserName = ApiMessage.Create(101_007, "Username is invalid, can only contain letters or digits");

            public static readonly ApiMessage InvalidEmail = ApiMessage.Create(101_008, "Email is invalid");

            public static readonly ApiMessage DuplicateUserName = ApiMessage.Create(101_009, "Username is already taken");

            public static readonly ApiMessage DuplicateEmail = ApiMessage.Create(101_010, "Email is already taken");

            public static readonly ApiMessage InvalidRoleName = ApiMessage.Create(101_011, "Role name is invalid");

            public static readonly ApiMessage DuplicateRoleName = ApiMessage.Create(101_012, "Role name is already taken");

            public static readonly ApiMessage UserAlreadyHasPassword = ApiMessage.Create(101_013, "User already has a password set");

            public static readonly ApiMessage UserLockoutNotEnabled = ApiMessage.Create(101_014, "Lockout is not enabled for this user");

            public static readonly ApiMessage UserAlreadyInRole = ApiMessage.Create(101_015, "Lockout is not enabled for this user");

            public static readonly ApiMessage UserNotInRole = ApiMessage.Create(101_016, "User is not in role");

            public static readonly ApiMessage PasswordTooShort = ApiMessage.Create(101_017, "Passwords must be at least 6 characters");

            public static readonly ApiMessage PasswordRequiresUniqueChars = ApiMessage.Create(101_018, "Passwords must use at least 1 different characters");

            public static readonly ApiMessage PasswordRequiresNonAlphanumeric = ApiMessage.Create(101_019, "Passwords must have at least one non alphanumeric character");

            public static readonly ApiMessage PasswordRequiresDigit = ApiMessage.Create(101_020, "Passwords must have at least one digit ('0'-'9')");

            public static readonly ApiMessage PasswordRequiresLower = ApiMessage.Create(101_021, "Passwords must have at least one lowercase ('a'-'z')");

            public static readonly ApiMessage PasswordRequiresUpper = ApiMessage.Create(101_022, "Passwords must have at least one uppercase ('A'-'Z')");

            public static readonly ApiMessage InvalidMaxApiTokenThreshold = ApiMessage.Create(101_023, "The max token count must be greater than or equals 1");

            public static readonly ApiMessage CouldNotDeleteMasterUser = ApiMessage.Create(101_024, "Could not delete the master user");
        }

        /// <summary>
        /// Code: 102x
        /// </summary>
        public class Role
        {
            public static readonly ApiMessage CouldNotDeleteMasterRole = ApiMessage.Create(102_001, "Could not delete the master role");

            public static readonly ApiMessage CouldNotDeleteDefaultRole = ApiMessage.Create(102_002, "Could not delete the default role");

            public static readonly ApiMessage NotFoundDefaultRole = ApiMessage.Create(102_003, "Could not found the default role");
            
            public static readonly ApiMessage CouldNotUpdateMasterRole = ApiMessage.Create(102_004, "Could not update master role's information");
        }

        /// <summary>
        /// Code: 103x
        /// </summary>
        public class Permission
        {
            public static readonly ApiMessage NotFoundRole = ApiMessage.Create(103_001, "Could not found role");

            public static readonly ApiMessage NotFoundUser = ApiMessage.Create(103_002, "Could not found user");
        }

        /// <summary>
        /// Code: 104x
        /// </summary>
        public class Authentication
        {
            public static readonly ApiMessage InvalidUsernameOrPassword = ApiMessage.Create(104_001, "Invalid username or password");

            public static readonly ApiMessage InvalidRefreshToken = ApiMessage.Create(104_002, "Invalid refresh token");
        }

        /// <summary>
        /// Code: 105x
        /// </summary>
        public class Setting
        {
            public static readonly ApiMessage ReloadConfigurationFailed = ApiMessage.Create(105_001, "Could not reload configuration");

            public static readonly ApiMessage UpdateSucceeded = ApiMessage.Create(105_002, "Update setting successfully");

            public static readonly ApiMessage InvalidData = ApiMessage.Create(105_003, "Invalid data");

            public static readonly ApiMessage NotFoundCollectionItem = ApiMessage.Create(105_004, "Not found collection item");

            public static readonly ApiMessage ValueOutOfRange = ApiMessage.Create(105_005, "Value out of range");

            public static readonly ApiMessage ValueRequired = ApiMessage.Create(105_006, "Value is required");

            public static readonly ApiMessage RegexNotMatch = ApiMessage.Create(105_007, "Regex not match");

            public static readonly ApiMessage KeyNotFound = ApiMessage.Create(105_008, "Key not found");

            public static readonly ApiMessage Unauthorized = ApiMessage.Create(105_009, "Unauthorized");

            public class Basic
            {
                public static readonly ApiMessage Root1 = ApiMessage.Create(105_101, "Cài đặt cơ bản 1");

                public static readonly ApiMessage Root2 = ApiMessage.Create(105_102, "Cài đặt cơ bản 2");

                public static readonly ApiMessage String = ApiMessage.Create(105_103, "Setting string title 1");

                public static readonly ApiMessage Boolean = ApiMessage.Create(105_104, "Setting boolean title 1");

                public static readonly ApiMessage Integer = ApiMessage.Create(105_105, "Setting integer title 1");

                public static readonly ApiMessage Double = ApiMessage.Create(105_106, "Setting double title 1");

                public static readonly ApiMessage Collection = ApiMessage.Create(105_107, "Collection setting title 1");
            }

            public class Smtp
            {
                public static readonly ApiMessage Root = ApiMessage.Create(105_201, "Smtp");

                public static readonly ApiMessage Host = ApiMessage.Create(105_202, "Host");

                public static readonly ApiMessage Port = ApiMessage.Create(105_203, "Port");

                public static readonly ApiMessage Name = ApiMessage.Create(105_204, "Name");

                public static readonly ApiMessage From = ApiMessage.Create(105_205, "From");

                public static readonly ApiMessage Username = ApiMessage.Create(105_206, "Username");

                public static readonly ApiMessage Password = ApiMessage.Create(105_207, "Password");

                public static readonly ApiMessage SecureSocket = ApiMessage.Create(105_208, "SecureSocket");
            }

            public class ConfirmAccount
            {
                public static readonly ApiMessage Root = ApiMessage.Create(105_301, "Confirm account");

                public static readonly ApiMessage Enable = ApiMessage.Create(105_302, "Turn on/off confirm account state");

                public static readonly ApiMessage FrontendUrl = ApiMessage.Create(105_303, "Url for building confirm url which send to user approve");

                public static readonly ApiMessage BackendUrl = ApiMessage.Create(105_304, "Url which frontend using to call BE to verify user");

                public static readonly ApiMessage EmailTitle = ApiMessage.Create(105_305, "Email title");

                public static readonly ApiMessage EmailTemplate = ApiMessage.Create(105_306, "Url of email template");
            }

            public class Authentiaction
            {
                public static readonly ApiMessage Root = ApiMessage.Create(105_401, "JWT Options");

                public static readonly ApiMessage TokenExpired = ApiMessage.Create(105_402, "Expired time for JWT Token (in minutes)");

                public static readonly ApiMessage RefreshTokenExpired = ApiMessage.Create(105_403, "Expired time for JWT Refresh Token (in minutes)");
            }

            public class RateLimit
            {
                public static readonly ApiMessage Root = ApiMessage.Create(105_501, "Rate Limit Options");

                public static readonly ApiMessage UserDefaultPoint = ApiMessage.Create(105_502, "User default point");

                public static readonly ApiMessage DefaultMinusPoint = ApiMessage.Create(105_503, "Default minus point");
            }

            public class InformationAccount
            {
                public static readonly ApiMessage Root = ApiMessage.Create(105_601, "Information account");

                public static readonly ApiMessage Enable = ApiMessage.Create(105_602, "Turn on/off send information account state");

                public static readonly ApiMessage FrontendUrl = ApiMessage.Create(105_603, "Url for building confirm url which send to user approve");

                public static readonly ApiMessage BackendUrl = ApiMessage.Create(105_604, "Url which frontend using to call BE to verify user");

                public static readonly ApiMessage EmailTitle = ApiMessage.Create(105_605, "Email title");

                public static readonly ApiMessage EmailTemplate = ApiMessage.Create(105_606, "Url of email template");
            }

            public class FCloud
            {
                public static readonly ApiMessage Root = ApiMessage.Create(105_701, "FCloud Options");

                public static readonly ApiMessage SecretId = ApiMessage.Create(105_702, "Secret Id");

                public static readonly ApiMessage SecretKey = ApiMessage.Create(105_703, "Secret Key");

                public static readonly ApiMessage AppId = ApiMessage.Create(105_703, "Application Id");
            }
        }

    }
}
