﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Grades")]
    public class Grade : AuditEntity<int>
    {
        public string Code { get; set; } = null!;

        public string Name { get; set; } = null!;

        public string? Description { get; set; }

        public virtual List<Class> Classes { get; set; } = [];
        public virtual List<Course> Courses { get; set; } = [];
    }
}
