﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class PronunciationErrorConfiguration : IEntityTypeConfiguration<PronunciationError>
    {
        public void Configure(EntityTypeBuilder<PronunciationError> builder)
        {
            builder.HasOne(x => x.Exercise)
                   .WithMany(e => e.PronunciationErrors)
                   .HasForeignKey(x => x.ExerciseId)
                   .IsRequired(false);
        }
    }
}
