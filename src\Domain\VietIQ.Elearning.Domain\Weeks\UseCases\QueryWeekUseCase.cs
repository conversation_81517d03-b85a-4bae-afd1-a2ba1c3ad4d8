﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Weeks
{
    public class QueryWeekUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IQueryWeekUseCase
    {
        public async Task<IApiResponse> GetAllAsync()
        {
            var repo = GetGenericRepository<Week>();
            var result = await repo.GetAsync(null, x => x.Course);

            return ApiResponse.Ok(Mapper.Map<IEnumerable<WeekModel>>(result));
        }

        public async Task<IApiResponse> GetAsync(IPaginationInput input)
        {
            var repo = GetGenericRepository<Week>();
            var predicate = PredicateBuilder.False<Week>();
            if (input.HasSearchTerm)
            {
                predicate = predicate.Or(x => EF.Functions.Like(x.LearningObjectives, input.SearchTermPattern));
                predicate = predicate.Or(x => EF.Functions.Like(x.Title, input.SearchTermPattern));
            }

            var result = await repo.AsQueryable().WhereIf(input.HasSearchTerm, predicate).ToPaginationAsync(input);
            return MapPagination<Week, WeekModel>(result).ToApiResponse();
        }

        public async Task<IApiResponse> GetByIdAsync(int id)
        {
            var repo = GetGenericRepository<Week, int>();
            var entity = await repo.FindAsync(x => x.Id == id);
            if (entity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<WeekModel>(entity));
        }
    }

}
