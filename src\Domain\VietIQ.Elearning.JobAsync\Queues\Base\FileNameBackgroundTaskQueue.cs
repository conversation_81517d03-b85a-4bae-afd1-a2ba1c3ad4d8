﻿using System.Threading.Channels;

namespace VietIQ.Elearning.JobAsync.Queues
{
    public sealed class FileNameBackgroundTaskQueue : IBackgroundTaskQueue
    {
        private readonly Channel<(Guid, string)> queue;

        public FileNameBackgroundTaskQueue()
        {
            queue = Channel.CreateUnbounded<(Guid, string)>();
        }

        public async Task EnqueueAsync(Guid batchId, string fileNames)
        {
            if (fileNames is null)
            {
                throw new ArgumentNullException(nameof(fileNames));
            }

            await queue.Writer.WriteAsync((batchId, fileNames));
        }

        public async Task<(Guid, string)> DequeueAsync(CancellationToken cancellationToken)
        {
            return await queue.Reader.ReadAsync(cancellationToken);
        }
    }
}
