﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Exercises
{
    public class QueryExerciseUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IQueryExerciseUseCase
    {
        public async Task<IApiResponse> GetAsync(IPaginationInput input)
        {
            var repo = GetGenericRepository<Exercise>();
            var predicate = PredicateBuilder.False<Exercise>();
            if (input.HasSearchTerm)
            {
                predicate = predicate.Or(x => EF.Functions.Like(x.QuestionData, input.SearchTermPattern));
            }

            var result = await repo.AsQueryable().WhereIf(input.HasSearchTerm, predicate).Include(x => x.Lesson).ToPaginationAsync(input);
            return MapPagination<Exercise, ExerciseModel>(result).ToApiResponse();
        }

        public async Task<IApiResponse> GetByIdAsync(int id)
        {
            var repo = GetGenericRepository<Exercise, int>();
            var entity = await repo.FindAsync(x => x.Id == id);
            if (entity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<ExerciseModel>(entity));
        }
    }

}
