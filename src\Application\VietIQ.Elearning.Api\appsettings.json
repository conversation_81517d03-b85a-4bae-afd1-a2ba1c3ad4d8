{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
    }
  },
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.EntityFrameworkCore.Database.Command": "Warning", // Giảm log EF Core
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}[{EventId}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/app-.log",
          "rollingInterval": "Day",
          "fileSizeLimitBytes": ********,
          "retainedFileCountLimit": 5,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {SourceContext}[{EventId}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  },
  //"Urls": "https://localhost:5001",
  "ConnectionStrings": {
    "DefaultConnection": "Host=postgres;Database=elearning;user id=admin;password=*********;",
    "RedisConnectionString": "redis:6379,ssl=False,abortConnect=False"
  },
  "JwtOption": {
    "SecretKey": "hellooooooooooooooooooooooooooooooooooooooooooooo",
    "TimeExpired": 30,
    "Issuer": "https://api.vietiq.com",
    "Audience": "https://api.vietiq.com"
  },
  "Caching": {
    "Memory": {

    },
    "Redis": {
      "ConnectionString": "redis:6379,ssl=False,abortConnect=False",
      "InstanceName": "VietIQSystemCache_"
    }
  },
  "IdentityOptions": {
    "SignIn": {
      "RequireConfirmedAccount": true
    }
  },
  "ConfirmAccount": {
    "Enable": true,
    "Url": "https://localhost/confirm"
  },
  "SendInfoAccount": {
    "Enable": true
  },
  "Template": {
    "ConfirmEmail": "asset\\templates\\confirm-email.html",
    "ChangePassword": "asset\\templates\\change-password.html",
    "ResetPassword": "asset\\templates\\reset-password.html"
  },
  "Upload": {
    "BasePath": "storage",
    "ImagePath": "images",
    "DocumentPath": "documents",
    "TempPath": "temp",
    "CategoryPath": "categories",
    "ProductPath": "products",
    "EditorPath": "editor",
    "AllowedType": {
      "Images": "jpg,jpeg, webpp, png, gif, svg",
      "Files": ".pdf, .docx, .xlsx,.txt"
    },
    "MaxSize": ********,
    "ImageContraints": {
      "Width": 2000,
      "Height": 2000
    },
    "ThumbSize": {
      "Small": {
        "Width": 150,
        "Height": 150
      },
      "Medium": {
        "Width": 300,
        "Height": 300
      }
    }
  },
  "AllowedHosts": "*",
  "RateLimitingOptions": {
    "EnableEndpointRateLimiting": true,
    "StackBlockedRequests": false,
    "RealIpHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    "GeneralRules": [
      {
        "Endpoint": "get:/api/caches/all-entries/*",
        "Period": "10s",
        "Limit": 5
      }
    ]
  },
  "UseDbSettings": true
}
