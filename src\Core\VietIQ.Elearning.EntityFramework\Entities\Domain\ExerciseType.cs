﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("ExerciseTypes")]
    public class ExerciseType : Entity<int>
    {
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string ScoringRules { get; set; } = null!;

        [NotMapped]
        public ExerciseTypeEnum TypeEnum => (ExerciseTypeEnum)Id;

        public List<Exercise> Exercises { get; set; } = [];
    }
}
