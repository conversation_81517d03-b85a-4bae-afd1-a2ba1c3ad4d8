﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class ExerciseItemConfiguration : IEntityTypeConfiguration<ExerciseItem>
    {
        public void Configure(EntityTypeBuilder<ExerciseItem> builder)
        {
            builder.HasQueryFilter(a => !a.Exercise.IsDeleted);

            builder.<PERSON><PERSON><PERSON>(e => e.Id);

            builder.HasIndex(x => x.ExerciseId);


            builder.HasOne(i => i.Exercise)
                   .WithMany(e => e.ExerciseItems)
                   .HasForeignKey(i => i.ExerciseId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(i => i.MediaFile)
                   .WithMany()
                   .HasForeignKey(i => i.MediaFileId)
                   .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
