﻿using System.ComponentModel.DataAnnotations;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.PronunciationResults
{
    public class PronunciationResultModel
    {
        public int Id { get; set; }

        public string RequestId { get; set; }

        public int ExerciseId { get; set; }

        public int ExerciseItemId { get; set; }

        public int UserId { get; set; }

        public int? Score { get; set; }

        public string? Feedback { get; set; }

        public string Status { get; set; }

        public int? ErrorCode { get; set; }

        public string? ErrorMessage { get; set; }

        public string? AudioFileUrl { get; set; }

        public double? ProcessingTime { get; set; }

        public double? WordsPerMin { get; set; }

        public double? SpeakingDuration { get; set; }

        public string? UserPhoneme { get; set; }

        public string? WordsScoreDetail { get; set; }

        public string? PhonemeScoreStatistics { get; set; }

        public string? StressResponse { get; set; }
    }
}
