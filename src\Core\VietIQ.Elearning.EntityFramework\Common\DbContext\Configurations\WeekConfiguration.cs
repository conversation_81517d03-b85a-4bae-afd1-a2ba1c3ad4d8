﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class WeekConfiguration : IEntityTypeConfiguration<Week>
    {
        public void Configure(EntityTypeBuilder<Week> builder)
        {
            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.HasIndex(c => c.CourseId)
                   .HasDatabaseName("IX_Week_CourseId");

            builder.HasMany(l => l.Lessons)
                   .WithOne(e => e.Week)
                   .HasForeignKey(e => e.WeekId);
        }
    }
}
