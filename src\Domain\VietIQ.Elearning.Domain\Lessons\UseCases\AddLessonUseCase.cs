﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Lessons
{
    public class AddLessonUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IAddLessonUseCase
    {
        public async Task<IApiResponse> AddAsync(AddLessonInput input)
        {
            var repo = GetGenericRepository<Lesson>();
            var entity = Map<Lesson>(input);
            await repo.AddAsync(entity);
            await CommitAsync();

            var output = Mapper.Map<LessonModel>(entity);
            return ApiResponse.Ok(output, MessageDefinition.Succeeded);
        }
    }

}
