﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace VietIQ.Elearning.EntityFramework
{
    public class BaseRepository<TEntity> : IBaseRepository<TEntity>
        where TEntity : class, IEntity
    {
        private readonly DbSet<TEntity> dbSet;

        public BaseRepository(ApplicationDbContext dbContext)
        {
            this.dbSet = dbContext.Set<TEntity>();
        }

        public IQueryable<TEntity> AsQueryable()
        {
            return this.dbSet.AsQueryable();
        }

        public IQueryable<TEntity> FromSqlRaw(string sql, params object[] parameters)
        {
            return this.dbSet.FromSqlRaw(sql, parameters);
        }

        public async Task<TEntity?> FindAsync(Expression<Func<TEntity, bool>> filter)
        {
            return await this.dbSet.Where(filter).FirstOrDefaultAsync();
        }

        public async Task<TEntity?> FindAsync<TProperty>(Expression<Func<TEntity, bool>> filter,
                                                         Expression<Func<TEntity, TProperty>> orderByPath,
                                                         int sortOrder)
        {
            var query = this.dbSet.Where(filter);
            if (sortOrder > 0)
            {
                return await query.OrderBy(orderByPath).FirstOrDefaultAsync();
            }

            return await query.OrderByDescending(orderByPath).FirstOrDefaultAsync();
        }

        public async Task<TEntity?> FindWithIncludesAsync(Expression<Func<TEntity, bool>> filter,
                                                         params Expression<Func<TEntity, object>>[] navigationPropertyPaths)
        {
            var query = this.dbSet.Where(filter);

            foreach (var item in navigationPropertyPaths)
            {
                query = query.Include(item);
            }

            return await query.FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<TEntity>> GetAsync(Expression<Func<TEntity, bool>>? filter = null)
        {
            if (filter == null)
            {
                return await this.dbSet.ToListAsync();
            }

            return await this.dbSet.Where(filter).ToListAsync();
        }

        public async Task<IEnumerable<TEntity>> GetAsync<TProperty>(
            Expression<Func<TEntity, bool>>? filter = null,
            params Expression<Func<TEntity, TProperty>>[] navigationPropertyPaths)
        {
            var query = filter != null
                ? this.dbSet.Where(filter)
                : this.dbSet.AsQueryable();
            foreach (var item in navigationPropertyPaths)
            {
                query = query.Include(item);
            }

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<TEntity>?> GetWithIncludesAsync(Expression<Func<TEntity, bool>>? filter = null,
                                                                params Expression<Func<TEntity, object>>[] navigationPropertyPaths)
        {
            var query = filter != null ? this.dbSet.Where(filter) : this.dbSet.AsQueryable();
            foreach (var item in navigationPropertyPaths)
            {
                query = query.Include(item);
            }

            return await query.ToListAsync();
        }

        public async Task<int> CountAsync(Expression<Func<TEntity, bool>>? filter = null)
        {
            if (filter == null)
            {
                return await this.dbSet.CountAsync();
            }

            return await this.dbSet.Where(filter).CountAsync();
        }

        public async Task<bool> AnyAsync(Expression<Func<TEntity, bool>>? filter = null)
        {
            var query = this.dbSet.AsQueryable();
            if (filter != null)
            {
                query = query.Where(filter);
            }

            return await query.AnyAsync();
        }

        public virtual async Task<TEntity?> AddAsync(TEntity entity)
        {
            var result = await this.dbSet.AddAsync(entity);
            return result.Entity;
        }

        public virtual async Task AddRangeAsync(IEnumerable<TEntity> entities)
        {
            await dbSet.AddRangeAsync(entities);
        }

        public virtual void Update(TEntity entity)
        {
            this.dbSet.Update(entity);
        }

        public virtual void UpdateRange(IEnumerable<TEntity> entities)
        {
            this.dbSet.UpdateRange(entities);
        }

        public virtual void Delete(TEntity entity)
        {
            this.dbSet.Remove(entity);
        }

        public void DeleteRange(IEnumerable<TEntity> entities)
        {
            this.dbSet.RemoveRange(entities);
        }

        public virtual void DeleteRange(Expression<Func<TEntity, bool>> expression)
        {
            this.dbSet.RemoveRange(dbSet.Where(expression));
        }
    }
}

