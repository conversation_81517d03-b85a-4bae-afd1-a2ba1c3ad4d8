﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Outboxes
{
    public class AddOutboxUseCase : BaseService, IAddOutboxUseCase
    {
        public AddOutboxUseCase(IUnitOfWork unitOfWork,
                                IMapper mapper) : base(unitOfWork, mapper)
        {
        }

        public async Task AddAsync<T>(string commandName, T data) where T : class
        {
            // Xử lý ghi nhận thông tin vào Outbox
            var outboxRepository = this.GetGenericRepository<Outbox>();
            await outboxRepository.AddAsync(new Outbox()
            {
                CommandName = commandName,
                Data = System.Text.Json.JsonSerializer.Serialize(data),
                IsCompleted = false,
            });

            await this.CommitAsync();
        }

        public async Task AddAsync(string commandName, object data)
        {
            await this.AddAsync<object>(commandName, data);
        }
    }
}
