﻿using Microsoft.AspNetCore.Mvc;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Authorization;
using VietIQ.Elearning.Api.Shared.Permissions;
using VietIQ.Elearning.Domain.PronunciationResults;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.JwtBearer;

namespace VietIQ.Elearning.Api.Controllers.Domain
{
    [ApiController]
    [Route("api/pronunciations")]
    public class PronunciationController(IPronunciationResultManager pronunciationResultManager,
                                         IJwtProvider jwtProvider) : ApiBaseController
    {
        private readonly IPronunciationResultManager pronunciationResultManager = pronunciationResultManager;
        private readonly IJwtProvider jwtProvider = jwtProvider;

        [HttpPost("init")]
        [FeatureAuthorize(PermissionNames.Exercise.View)]
        public IActionResult InitSdk([FromBody] InitSdkInput input)
        {
            var token = jwtProvider.GenerateToken(input.UserId);
            return ApiResponse.Ok(new { token, expiresAt = DateTime.UtcNow.AddHours(24) }, "Init Sdk success")
                              .ToActionResult();
        }

        [HttpPost("callback")]
        public async Task<IActionResult> ReceiveCallbackAsync([FromBody] dynamic input)
        {
            var signature = Request.Headers["X-Callback-Signature"].ToString();

            var inputData = new ReceivePronunciationResultInput
            {
                ApiKey = Request.Headers["X-Callback-ApiKey"].ToString(),
                AppId = Request.Headers["X-Callback-AppId"].ToString(),
                Signature = signature,
                Input = input
            };

            var result = await pronunciationResultManager.ReceivePronunciationResultCallbackAsync(inputData);
            return result.ToActionResult();
        }

        [HttpGet("result/{requestId}")]
        [FeatureAuthorize(PermissionNames.Exercise.View)]
        public async Task<IActionResult> GetResultAsync(string requestId)
        {
            var currentUserId = GetUserId();
            if (currentUserId == null)
            {
                return ApiResponse.Unauthorized("User not authenticated").ToActionResult();
            }

            var result = await pronunciationResultManager.GetPronunciationResultByIdAsync(requestId, currentUserId.Value);
            return result.ToActionResult();
        }
    }
}
