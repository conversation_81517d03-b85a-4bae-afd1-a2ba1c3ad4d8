﻿using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using VietIQ.Elearning.EntityFramework.Entities.Domain;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    public class User : IdentityUser<int>, IEntity<int>, IFullyEntity<int>
    {
        public const string MasterName = "admin";

        public User()
        {
            this.RefreshTokens = [];
        }

        [MaxLength(EntityLength.Short)]
        public string? FirstName { get; set; }

        [MaxLength(EntityLength.Short)]
        public string? LastName { get; set; }

        [MaxLength(EntityLength.Normal)]
        public string? Address { get; set; }

        public bool IsEnabled { get; set; }

        public bool IsDeleted { get; set; }

        [MaxLength(32)]
        public string? Token { get; set; }

        public string? ImageUrl { get; set; }

        public int? ClassId { get; set; }

        public int MaxTokenCount { get; set; }

        public DateTimeOffset? LastLoginTime { get; set; }

        public DateTimeOffset CreatedTime { get; set; }

        public DateTimeOffset? UpdatedTime { get; set; }

        public DateTimeOffset? DeletedTime { get; set; }

        [MaxLength(EntityLength.Short)]
        public string? DeletedBy { get; set; }

        [MaxLength(EntityLength.Short)]
        public string? CreatedBy { get; set; }

        [MaxLength(EntityLength.Short)]
        public string? UpdatedBy { get; set; }

        public virtual List<RefreshToken> RefreshTokens { get; set; }
        public virtual List<ApiToken>? ApiTokens { get; set; }
        public virtual Class? Class { get; set; }
        public virtual List<SubscriptionUser> SubscriptionUsers { get; set; } = [];
        public virtual List<UserProgress> Progresses { get; set; } = [];
        public virtual List<UserAnswer> UserAnswers { get; set; } = [];
        public virtual List<WeeklyStat> WeeklyStats { get; set; } = [];
        public virtual List<PronunciationError> PronunciationErrors { get; set; } = [];
        public virtual List<PronunciationResult> PronunciationResults { get; set; } = [];
        public virtual List<AIRecommendation> AIRecommendations { get; set; } = [];
        public virtual ICollection<UserNotification>? UserNotifications { get; set; }
        public virtual ICollection<Permission> Permissions { get; set; } = [];
    }
}