﻿namespace VietIQ.Elearning.EntityFramework
{
    /// <summary>
    /// The type of exercise.
    /// This enum is used to categorize different types of exercises in the system.
    /// Must match the ExerciseType enum in the domain layer.
    /// </summary>
    public enum ExerciseTypeEnum
    {
        AudioChoice = 1,
        TextInput,
        Speaking,
        ImageChoice,
        Translation,
        Writing,
        AudioFill,
        Pronunciation,
        SentenceOrdering,
        Matching,
        TextChoice
    }
}
