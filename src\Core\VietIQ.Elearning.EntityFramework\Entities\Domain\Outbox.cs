﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework
{
    [Table("Outboxes")]
    public class Outbox : AuditEntity<Guid>
    {
        [MaxLength(32)]
        [Required]
        public string CommandName { get; set; } = null!;

        public string Data { get; set; } = null!;

        public bool IsCompleted { get; set; }

        public virtual IEnumerable<OutboxHistory>? Histories { get; set; }
    }
}
