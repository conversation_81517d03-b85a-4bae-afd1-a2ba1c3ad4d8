﻿using AutoMapper;
using VietIQ.Elearning.Core.Authentication;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;
using Microsoft.AspNetCore.Identity;

namespace VietIQ.Elearning.Core.Authencation
{
    public class AuthenticateUseCase : BaseService, IAuthenticateUseCase
    {
        private readonly UserManager<User> userManager;
        private readonly IAuthManager authManager;
        private readonly SignInManager<User> signInManager;

        public AuthenticateUseCase(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IAuthManager authManager,
            SignInManager<User> signInManager,
            UserManager<User> userManager)
                : base(unitOfWork, mapper)
        {
            this.authManager = authManager;
            this.signInManager = signInManager;
            this.userManager = userManager;
        }

        public async Task<IApiResponse<AuthOutput>> AuthenticateAsync(AuthInput model, string ipAddress)
        {
            var user = await userManager.FindByNameAsync(model.Username);
            if (user == null)
            {
                return ApiResponse.BadRequest<AuthOutput>(MessageDefinition.Authentication.InvalidUsernameOrPassword);
            }

            user.LastLoginTime = DateTimeOffset.UtcNow;

            var signInResult = await signInManager.CheckPasswordSignInAsync(user, model.Password, false);
            if (!signInResult.Succeeded)
            {
                return ApiResponse.BadRequest<AuthOutput>(MessageDefinition.Authentication.InvalidUsernameOrPassword);
            }

            var authOutput = await authManager.GenerateTokenAsync(user, ipAddress);

            // Xóa refresh token đã cũ
            await authManager.RemoveOldRefreshTokensAsync(user.Id);

            await CommitAsync();

            return ApiResponse.Ok(authOutput, MessageDefinition.Succeeded);
        }
    }
}
