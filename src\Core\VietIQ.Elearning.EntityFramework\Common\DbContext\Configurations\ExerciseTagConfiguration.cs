﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class ExerciseTagConfiguration : IEntityTypeConfiguration<ExerciseTag>
    {
        public void Configure(EntityTypeBuilder<ExerciseTag> builder)
        {
            builder.HasKey(et => new { et.ExerciseId, et.TagId });

            builder.HasOne(x => x.Exercise)
                   .WithMany(e => e.ExerciseTags)
                   .HasForeignKey(x => x.ExerciseId)
                   .IsRequired(false);

            builder.HasOne(et => et.Exercise)
                .WithMany(e => e.ExerciseTags)
                .HasForeignKey(et => et.ExerciseId);

            builder.HasOne(et => et.Tag)
                .WithMany(t => t.ExerciseTags)
                .HasForeignKey(et => et.TagId);
        }
    }
}
