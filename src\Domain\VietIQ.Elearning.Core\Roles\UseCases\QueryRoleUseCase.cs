﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Core.Roles
{
    public class QueryRoleUseCase(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        UserManager<User> userManager,
        RoleManager<Role> roleManager) : BaseService(unitOfWork, mapper), IQueryRoleUseCase
    {
        private readonly UserManager<User> userManager = userManager;
        private readonly RoleManager<Role> roleManager = roleManager;

        public async Task<IApiResponse> GetAsync(IPaginationInput paginationInput)
        {
            var roleRepository = this.GetGenericRepository<Role>();

            var searchPredicate = PredicateBuilder.False<Role>();
            if (paginationInput.HasSearchTerm)
            {
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.Name, paginationInput.SearchTermPattern));
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.Description!, paginationInput.SearchTermPattern));
            }

            var rolePagination = await roleRepository
                                        .AsQueryable()
                                        .WhereIf(paginationInput.HasSearchTerm, searchPredicate)
                                        .ToPaginationAsync(paginationInput);

            var roleModels = MapPagination<Role, RoleModel>(rolePagination);
            return roleModels.ToApiResponse();
        }

        public async Task<IApiResponse> GetAllAsync()
        {
            var roleRepository = this.GetGenericRepository<Role>();
            var roles = await roleRepository.GetAsync();

            var roleModels = Mapper.Map<IEnumerable<RoleModel>>(roles);
            return ApiResponse.Ok(roleModels);
        }

        public async Task<RoleModel> GetDefaultRoleAsync()
        {
            var roleRepository = this.GetGenericRepository<Role>();
            var defaultRole = await roleRepository.FindAsync(x => x.IsDefault);

            return Mapper.Map<RoleModel>(defaultRole);
        }

        public async Task<IApiResponse> GetGrantedRolesAsync(int userId)
        {
            var user = await userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            var grantedRoles = await userManager.GetRolesAsync(user);
            var roles = await roleManager.Roles.ToListAsync();

            var output = roles.Select(x => new GetGrantedRoleOutput()
            {
                RoleName = x.Name ?? string.Empty,
                IsGranted = grantedRoles.Any(k => k == x.Name)
            }).ToList();

            return ApiResponse.Ok(output);
        }
    }
}
