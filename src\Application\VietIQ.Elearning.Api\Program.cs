﻿using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Identity;
using Serilog;
using System.Text.Json;
using System.Text.Json.Serialization;
using VietIQ.Elearning.Api.Core.Conventions;
using VietIQ.Elearning.Api.InputValidation;
using VietIQ.Elearning.Api.Logs;
using VietIQ.Elearning.Api.Shared;
using VietIQ.Elearning.Api.Shared.Filters;
using VietIQ.Elearning.Api.Shared.Middleware;
using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.Api.Shared.Settings;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;
using VietIQ.Elearning.Infrastructure.Caching;
using VietIQ.Elearning.JwtBearer;

namespace VietIQ.Elearning.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add DbContext
            builder.Services.AddApplicationDbContext(builder.Configuration);

            // Add Db configuration
            builder.Host.AddDbConfiguration(builder.Services, builder.Configuration);

            // Add Identity
            builder.Services
                .AddIdentity<User, Role>(option =>
                {
                    var configName = "IdentityOptions:SignIn:RequireConfirmedAccount";
                    option.SignIn.RequireConfirmedAccount = Convert.ToBoolean(builder.Configuration[configName]);
                })
                .AddTokenProvider<DataProtectorTokenProvider<User>>(TokenOptions.DefaultProvider)
                .AddEntityFrameworkStores<ApplicationDbContext>();

            builder.Services.AddIdentityCore<User>();

            builder.Services.AddControllers(opt =>
                {
                    opt.Conventions.Add(new DynamicApiConvention(
                        new DefaultSelectControllerImpl(),
                        new RestfulRouteFactory()));
                    opt.Conventions.Add(new ConventionalControllerGroupingConvention());
                }).ConfigureApplicationPartManager(manager =>
                 {
                     manager.FeatureProviders.Add(new DynamicApiControllerFeatureProvider(new DefaultSelectControllerImpl()));
                 })
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                    options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
                    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                });

            // Add more register
            ServiceRegister.Register(builder.Services, builder.Configuration, builder.Environment);

#if DEBUG
            DynamicApiValidator.Validate(builder.Services, throwOnViolation: false);
#else
            DynamicApiValidator.Validate(builder.Services); // strict: throw nếu vi phạm
#endif

            builder.Logging.ClearProviders();
            builder.Host.AddLogging(builder.Services, builder.Configuration);

            // Add input validation
            builder.Services.AddInputValidation();

            var app = builder.Build();

            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            });

            app.UseMiddleware<ExceptionHandlerMiddleware>();
            app.UseMiddleware<JwtInHeaderMiddleware>();
            //app.UseMiddleware<RateLimitHandlerMiddleware>();

            // Use Rate Limiting
            // app.UseRateLimiting();

            app.UseCustomCors();
            app.SeedData();

            var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), app.Configuration[SettingKey.Upload.BasePath] ?? "uploads");
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }

            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(uploadsFolder),
                RequestPath = $"/{app.Configuration[SettingKey.Upload.BasePath] ?? "uploads"}"
            });

            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(options =>
                {
                    options.SwaggerEndpoint("/swagger/all/swagger.json", "All APIs");
                    options.SwaggerEndpoint("/swagger/core/swagger.json", "Core APIs");
                    options.SwaggerEndpoint("/swagger/dynamic/swagger.json", "Dynamic APIs");
                    options.DocumentTitle = "VietIQ Elearning API Docs";
                });
            }

            // Configure the HTTP request pipeline.
            // app.UseHttpsRedirection();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseSerilogRequestLogging();
            app.MapControllers();

            // Delete all cache files
            MemoryCache.DeleteAllCacheFiles();

            app.Run();
        }
    }
}
