using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ActionConstraints;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.RateLimiting;
using System.Reflection;

namespace VietIQ.Elearning.DynamicApi
{
    public class DynamicApiConvention : IApplicationModelConvention
    {
        private readonly ISelectController selectController;
        private readonly IActionRouteFactory actionRouteFactory;

        public DynamicApiConvention(ISelectController selectController, IActionRouteFactory actionRouteFactory)
        {
            this.selectController = selectController;
            this.actionRouteFactory = actionRouteFactory;
        }

        public void Apply(ApplicationModel application)
        {
            foreach (var controller in application.Controllers)
            {
                if (!selectController.IsController(controller.ControllerType))
                {
                    continue;
                }

                ConfigureDynamicA<PERSON>(controller);
            }
        }

        private void ConfigureDynamicApi(ControllerModel controller)
        {
            if (controller.ControllerType.IsDefined(typeof(NonDynamicApiAttribute), inherit: true))
            {
                // Không expose controller + toàn bộ actions
                controller.ApiExplorer.IsVisible = false;
                controller.Actions.Clear(); // hoặc return;
                return;
            }

            var controllerName = actionRouteFactory.GetControllerName(controller.ControllerType);
            var moduleGroup = GetGroupNameFromNamespace(controller.ControllerType.Namespace);

            controller.ApiExplorer.GroupName = "all";
            controller.ApiExplorer.IsVisible = true;

            var isDynamicApiController = typeof(IDynamicApi).IsAssignableFrom(controller.ControllerType.AsType());

            foreach (var action in controller.Actions.ToList())
            {
                var method = action.ActionMethod;

                if (method.IsDefined(typeof(NonDynamicApiAttribute), inherit: true))
                {
                    controller.Actions.Remove(action);
                    continue;
                }

                var hasInclude =
                    method.IsDefined(typeof(DynamicApiMethodAttribute), true) ||
                    method.IsDefined(typeof(DynamicHttpGetAttribute), true) ||
                    method.IsDefined(typeof(DynamicHttpPostAttribute), true) ||
                    method.IsDefined(typeof(DynamicHttpPutAttribute), true) ||
                    method.IsDefined(typeof(DynamicHttpDeleteAttribute), true);

                var hasExclude =
                    method.IsDefined(typeof(NonDynamicMethodAttribute), true) ||
                    method.IsDefined(typeof(NonFormatResultAttribute), true);

                if (isDynamicApiController && hasExclude)
                {
                    controller.Actions.Remove(action);
                    continue;
                }

                // Gán route nếu chưa có (dùng factory)
                foreach (var selector in action.Selectors)
                {
                    selector.AttributeRouteModel ??= actionRouteFactory.CreateActionRouteModel(
                        areaName: string.Empty,
                        controllerName: controllerName,
                        action: action
                    );

                    // Gán HttpMethod nếu chưa có
                    if (!selector.ActionConstraints?.OfType<HttpMethodActionConstraint>().Any() ?? true)
                    {
                        var httpAttr = ResolveHttpMethodAttribute(method.Name, method.DeclaringType?.Name ?? "");
                        if (httpAttr != null)
                        {
                            selector.ActionConstraints?.Add(new HttpMethodActionConstraint(httpAttr.HttpMethods));
                            selector.EndpointMetadata.Add(httpAttr);
                        }
                    }
                }

                action.ApiExplorer.GroupName = moduleGroup;
                action.ApiExplorer.IsVisible = true;

                var dynAuthAttr = method.GetCustomAttribute<DynamicAuthorizeAttribute>()
                                  ?? controller.ControllerType.GetCustomAttribute<DynamicAuthorizeAttribute>();

                if (dynAuthAttr != null)
                {
                    // Gán FeatureAuthorizeAttribute động vào Metadata
                    var policy = "FEATURE_PERMISSION::" + string.Join(",", dynAuthAttr.Permissions);
                    action.Filters.Add(new AuthorizeFilter(policy));
                }
            }
        }

        private static HttpMethodAttribute? ResolveHttpMethodAttribute(string methodName, string declaringClassName)
        {
            methodName = methodName.ToLowerInvariant();
            declaringClassName = declaringClassName.ToLowerInvariant();

            if (methodName.StartsWith("get")) return new HttpGetAttribute();
            if (methodName.StartsWith("create") || methodName.StartsWith("add") || methodName.StartsWith("insert"))
                return new HttpPostAttribute();
            if (methodName.StartsWith("update") || methodName.StartsWith("edit") || methodName.StartsWith("put"))
                return new HttpPutAttribute();
            if (methodName.StartsWith("delete") || methodName.StartsWith("remove"))
                return new HttpDeleteAttribute();

            // fallback từ class name
            if (declaringClassName.StartsWith("get")) return new HttpGetAttribute();
            if (declaringClassName.StartsWith("create") || declaringClassName.StartsWith("add") || declaringClassName.StartsWith("insert"))
                return new HttpPostAttribute();
            if (declaringClassName.StartsWith("update") || declaringClassName.StartsWith("edit") || declaringClassName.StartsWith("put"))
                return new HttpPutAttribute();
            if (declaringClassName.StartsWith("delete") || declaringClassName.StartsWith("remove"))
                return new HttpDeleteAttribute();

            return null;
        }

        private static string GetGroupNameFromNamespace(string? ns)
        {
            if (string.IsNullOrWhiteSpace(ns))
            {
                return "default";
            }
            // Giả sử namespace có dạng "VietIQ.Elearning.ModuleName.Controllers"
            var parts = ns.Split('.');
            return parts.Last().ToLowerInvariant();
        }
    }
}
