﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Classes")]
    public class Class : AuditEntity<int>
    {
        public int OrganizationId { get; set; }

        public int GradeId { get; set; }
        
        public string Code { get; set; } = null!;
        
        public string Name { get; set; } = null!;
        
        public int AcademicYear { get; set; }

        public virtual Organization Organization { get; set; } = null!;
        public virtual Grade Grade { get; set; } = null!;
        public virtual List<User> Users { get; set; } = [];
    }
}
