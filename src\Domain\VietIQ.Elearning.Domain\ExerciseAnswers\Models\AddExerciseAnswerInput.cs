﻿using Microsoft.AspNetCore.Http;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.ExerciseAnswers
{
    public class AddExerciseAnswerInput
    {
        public int ExerciseId { get; set; }

        public MediaType Type { get; set; }

        public string? Content { get; set; }

        public bool IsCorrect { get; set; }

        public int DisplayOrder { get; set; }

        public int? OrderIndex { get; set; }

        public string? Explanation { get; set; }

        public string? GroupKey { get; set; }

        public string? MatchKey { get; set; }

        public IFormFile? File { get; set; }
    }
}
