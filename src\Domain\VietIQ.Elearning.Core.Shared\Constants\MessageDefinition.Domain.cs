﻿using VietIQ.Elearning.DynamicApi.Response;

namespace VietIQ.Elearning.Core.Constants
{
    public static partial class MessageDefinition
    {
        /// <summary>
        /// Code: 201x
        /// </summary>
        public class Week
        {
            public static readonly ApiMessage WeekNumberDuplicated = ApiMessage.Create(201_001, "Duplication week number");

            public static readonly ApiMessage WeekNotFound = ApiMessage.Create(201_002, "Week not found");

            public static readonly ApiMessage WeekAlreadyExists = ApiMessage.Create(201_003, "Week already exists");
        }

        /// <summary>
        /// code: 202x
        /// </summary>
        public class Class
        {
            public static readonly ApiMessage ClassNotFound = ApiMessage.Create(202_001, "Class not found");
            public static readonly ApiMessage ClassAlreadyExists = ApiMessage.Create(202_002, "Class already exists");
            public static readonly ApiMessage ClassNameDuplicated = ApiMessage.Create(202_003, "Class name duplicated");
        }

        ///<summary>
        /// Code: 203x
        ///</summary> 
        public class Course
        {
            public static readonly ApiMessage CourseNotFound = ApiMessage.Create(203_001, "Course not found");
            public static readonly ApiMessage CourseAlreadyExists = ApiMessage.Create(203_002, "Course already exists");
            public static readonly ApiMessage CourseNameDuplicated = ApiMessage.Create(203_003, "Course name duplicated");
        }


        /// <summary>
        /// code: 204x
        /// </summary>
        public class Organization
        {
            public static readonly ApiMessage OrganizationNotFound = ApiMessage.Create(204_001, "Organization not found");
            public static readonly ApiMessage OrganizationAlreadyExists = ApiMessage.Create(204_002, "Organization already exists");
            public static readonly ApiMessage OrganizationNameDuplicated = ApiMessage.Create(204_003, "Organization name duplicated");
        }


        /// <summary>
        /// Code: 205x
        /// </summary>
        public class Session
        {
            public static readonly ApiMessage SessionNotFound = ApiMessage.Create(205_001, "Session not found");
            public static readonly ApiMessage SessionAlreadyExists = ApiMessage.Create(205_002, "Session already exists");
            public static readonly ApiMessage SessionNameDuplicated = ApiMessage.Create(205_003, "Session name duplicated");
        }

        /// <summary>
        /// code: 206x
        /// </summary>
        public class Exercise
        {
            public static readonly ApiMessage ExerciseNotFound = ApiMessage.Create(206_001, "Exercise not found");
            public static readonly ApiMessage ExerciseAlreadyExists = ApiMessage.Create(206_002, "Exercise already exists");
            public static readonly ApiMessage ExerciseNameDuplicated = ApiMessage.Create(206_003, "Exercise name duplicated");
        }

        /// <summary>
        /// Code: 207x
        /// </summary>
        public class ApiToken
        {
            public static readonly ApiMessage InvalidApiToken = ApiMessage.Create(207_001, "Invalid ApiToken");

            public static readonly ApiMessage CouldNotDeleteDefaultApiToken = ApiMessage.Create(207_002, "Could not delete the default API token");

            public static readonly ApiMessage ApiTokenCountMaximum = ApiMessage.Create(207_003, "Your number of tokens has reached the maximum");
        }

        /// <summary>
        /// Code: 208x
        /// </summary>
        public class Lesson
        {
            public static readonly ApiMessage LessonNotFound = ApiMessage.Create(208_001, "Lesson not found");
            public static readonly ApiMessage LessonAlreadyExists = ApiMessage.Create(208_002, "Lesson already exists");
            public static readonly ApiMessage LessonNameDuplicated = ApiMessage.Create(208_003, "Lesson name duplicated");
        }

        /// <summary>
        /// Code: 209x
        /// </summary>
        public class Grade
        {
            public static readonly ApiMessage GradeNotFound = ApiMessage.Create(209_001, "Grade not found");
            public static readonly ApiMessage GradeAlreadyExists = ApiMessage.Create(209_002, "Grade already exists");
            public static readonly ApiMessage GradeNameDuplicated = ApiMessage.Create(209_003, "Grade name duplicated");
            public static readonly ApiMessage GradeCodeDuplicated = ApiMessage.Create(209_004, "Grade code duplicated");
        }
    }
}
