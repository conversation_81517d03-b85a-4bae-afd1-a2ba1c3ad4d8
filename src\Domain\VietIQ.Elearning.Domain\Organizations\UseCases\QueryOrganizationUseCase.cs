﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.Core.Users;
using VietIQ.Elearning.Domain.Classes;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Organizations
{
    public class QueryOrganizationUseCase(IUnitOfWork unitOfWork,
                                          IMapper mapper) : BaseService(unitOfWork, mapper), IQueryOrganizationUseCase
    {
        public async Task<IApiResponse> GetAsync(IPaginationInput paginationInput)
        {
            var repository = GetGenericRepository<Organization>();

            var searchPredicate = PredicateBuilder.False<Organization>();
            if (paginationInput.HasSearchTerm)
            {
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.Name, paginationInput.SearchTermPattern));
                searchPredicate = searchPredicate.Or(x => EF.Functions.Like(x.Type, paginationInput.SearchTermPattern));
            }

            var categories = await repository
                                .AsQueryable()
                                .WhereIf(paginationInput.HasSearchTerm, searchPredicate)
                                .ToPaginationAsync(paginationInput);

            var categoriesModel = MapPagination<Organization, OrganizationModel>(categories);
            return categoriesModel.ToApiResponse();
        }

        public async Task<IApiResponse> GetAllAsync()
        {
            var repository = this.GetGenericRepository<Organization>();
            var organizations = await repository.GetAsync(x => x.Status == StatusType.Active, x => x.Classes);

            var organizationModels = Mapper.Map<IEnumerable<OrganizationModel>>(organizations);
            return ApiResponse.Ok(organizationModels);
        }

        public async Task<IApiResponse> GetOrganizationByIdAsync(int organizationId)
        {
            var repository = GetGenericRepository<Organization, int>();
            var category = await repository.FindAsync(x => x.Id == organizationId);
            if (category == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Organization.OrganizationNotFound);
            }

            var organizationModel = Mapper.Map<OrganizationModel>(category);
            return ApiResponse.Ok(organizationModel);
        }

        public async Task<IApiResponse> GetUsersByOrganizationIdAsync(int organizationId)
        {
            var organizationrRepo = GetGenericRepository<Organization>();

            var organization = await organizationrRepo.FindAsync(x => x.Id == organizationId);
            if (organization == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Organization.OrganizationNotFound);
            }

            var userRepo = GetGenericRepository<User>();
            var users = await userRepo.FindAsync(u => u.Class != null && u.Class.OrganizationId == organizationId);
            return ApiResponse.Ok(Mapper.Map<IEnumerator<UserModel>>(users));
        }

        public async Task<IApiResponse> GetClassesByOrganizationIdAsync(int organizationId)
        {
            var organizationrRepo = GetGenericRepository<Organization>();

            var organization = await organizationrRepo.FindAsync(x => x.Id == organizationId);
            if (organization == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Organization.OrganizationNotFound);
            }

            var classRepo = GetGenericRepository<Class>();
            var classes = await classRepo.FindAsync(x => x.OrganizationId == organizationId);
            return ApiResponse.Ok(Mapper.Map<IEnumerator<ClassModel>>(classes));
        }
    }
}
