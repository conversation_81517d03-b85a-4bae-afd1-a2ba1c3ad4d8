﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Grades
{
    public class UpdateGradeUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IUpdateGradeUseCase
    {
        public async Task<IApiResponse> UpdateAsync(int id, UpdateGradeInput input)
        {
            var repo = GetGenericRepository<Grade>();
            var entity = await repo.FindAsync(x => x.Id == id);
            if (entity == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            entity = Mapper.Map(input, entity);
            repo.Update(entity);
            await CommitAsync();

            return ApiResponse.Ok(new UpdateGradeOutput
            {
                Input = input,
                Output = Mapper.Map<GradeModel>(entity)
            }, MessageDefinition.Succeeded);
        }
    }
}
