﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace VietIQ.Elearning.EntityFramework
{
    public class OutboxConfiguration : IEntityTypeConfiguration<Outbox>
    {
        public void Configure(EntityTypeBuilder<Outbox> builder)
        {
            builder.<PERSON><PERSON>ey(c => c.Id);

            builder.HasMany(x => x.Histories)
                  .WithOne(x => x.Outbox)
                  .HasForeignKey(x => x.OutboxId)
                  .HasConstraintName("FK_Outbox_History")
                  .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
