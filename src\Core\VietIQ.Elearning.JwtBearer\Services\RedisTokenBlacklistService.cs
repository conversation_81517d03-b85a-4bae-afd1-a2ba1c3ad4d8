﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;

namespace VietIQ.Elearning.JwtBearer
{
    public class RedisTokenBlacklistService(IDistributedCache cache, ILogger<RedisTokenBlacklistService> logger) : ITokenBlacklistService
    {
        private readonly IDistributedCache _cache = cache;
        private readonly ILogger<RedisTokenBlacklistService> _logger = logger;

        public async Task BlacklistAllUserTokensAsync(int userId)
        {
            // Store user token invalidation timestamp
            var key = GetUserInvalidationKey(userId);
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();

            await _cache.SetStringAsync(key, timestamp, new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromDays(30) // Keep for max token lifetime
            });

            _logger.LogInformation("All tokens for user {UserId} invalidated", userId);
        }

        public async Task BlacklistTokenAsync(string jti, DateTime expiration)
        {
            try
            {
                var key = GetBlacklistKey(jti);
                var ttl = expiration - DateTime.UtcNow;

                if (ttl > TimeSpan.Zero)
                {
                    await _cache.SetStringAsync(key, "blacklisted", new DistributedCacheEntryOptions
                    {
                        AbsoluteExpiration = expiration
                    });

                    _logger.LogInformation("Token {Jti} blacklisted until {Expiration}", jti, expiration);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to blacklist token {Jti}", jti);
                throw;
            }
        }

        public async Task<bool> IsTokenBlacklistedAsync(string jti)
        {
            try
            {
                var key = GetBlacklistKey(jti);
                var result = await _cache.GetStringAsync(key);
                return !string.IsNullOrEmpty(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check blacklist for token {Jti}", jti);
                // Fail secure - assume token is blacklisted if we can't check
                return true;
            }
        }

        private static string GetBlacklistKey(string jti) => $"blacklist:token:{jti}";
        private static string GetUserInvalidationKey(int userId) => $"blacklist:user:{userId}";
    }
}
