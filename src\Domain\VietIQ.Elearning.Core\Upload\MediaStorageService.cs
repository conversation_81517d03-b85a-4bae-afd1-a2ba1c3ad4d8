﻿using Microsoft.AspNetCore.Http;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Core.Upload
{
    public class MediaStorageService(IUploadService uploadService) : IMediaStorageService
    {
        public async Task<bool> DeleteFileFromPathAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                return false;
            }

            try
            {
                return await uploadService.DeleteFileFromPathAsync(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting file: {ex.Message}");
                return false;
            }
        }

        public async ValueTask<string> UploadAsync(IFormFile file, MediaType type, string folder = "exercise")
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("File is null or empty.");
            }

            var fileName = file.FileName;

            return type switch
            {
                MediaType.Image => await uploadService.UploadImageAsync(new UploadImageModel
                {
                    File = file,
                    FileName = fileName,
                    DestinationFolder = folder
                }),

                MediaType.Audio or MediaType.Video => await uploadService.UploadFileAsync(new UploadFileModel
                {
                    File = file,
                    FileName = fileName,
                    DestinationFolder = folder
                }),

                _ => throw new NotSupportedException($"Media type '{type}' is not supported for upload.")
            };
        }
    }
}
