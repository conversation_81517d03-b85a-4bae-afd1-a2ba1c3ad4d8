﻿using VietIQ.Elearning.JobAsync.Queues;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;

namespace VietIQ.Elearning.JobAsync
{
    public static class AddJobExtensions
    {
        public static void AddQuartzJob(this IServiceCollection services)
        {
            services.AddSingleton<IJobFactory, JobFactory>();
            services.AddSingleton<ISchedulerFactory, StdSchedulerFactory>();
            services.AddSingleton<QuartzJobRunner>();
            services.AddHostedService<QuartzHostedService>();

            services.AddSingleton<OutboxJob>();
            services.AddScoped<PrintGuidJob>();

            services.AddSingleton(new JobSchedule(
                jobType: typeof(OutboxJob),
                cronExpression: "0/5 * * * * ?")); //every 10 seconds

            services.AddSingleton(new JobSchedule(
                jobType: typeof(PrintGuidJob),
                cronExpression: "0/5 * * * * ?")); //every 5 seconds
        }
    }
}
