﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Lessons")]
    public class Lesson : Entity<int>
    {
        public int WeekId { get; set; }
        public int Order { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }

        public virtual Week Week { get; set; } = null!;
        public virtual ICollection<Exercise> Exercises { get; set; } = [];
        public virtual ICollection<UserProgress> Progresses { get; set; } = [];
    }
}
