﻿using AutoMapper;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Upload;
using VietIQ.Elearning.Domain.ExerciseAnswers;
using VietIQ.Elearning.Domain.ExerciseItems;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Exercises
{
    public class UpdateExerciseUseCase(IUnitOfWork unitOfWork,
                                       IMapper mapper,
                                       IMediaStorageService storageService) : BaseExerciseUseCase(unitOfWork, mapper, storageService), IUpdateExerciseUseCase
    {
        public async Task<IApiResponse> PatchItemAsync(int id, UpdateExerciseItemInput input)
        {
            var itemRepo = GetGenericRepository<ExerciseItem>();

            var item = await itemRepo.FindWithIncludesAsync(i => i.Id == id, i => i.Exercise);
            if (item == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            //foreach (var media in item.MediaLinks)
            //{
            //    if (!string.IsNullOrWhiteSpace(media.FilePath))
            //    {
            //        await storageService.DeleteFileFromPathAsync(media.FilePath);
            //    }
            //}

          
            //item.MediaLinks.Clear();

            //item.DisplayType = input.DisplayType;
            //item.MediaLinks = await CreateMediaAsync(input.Medias);

            itemRepo.Update(item);
            await CommitAsync();

            return ApiResponse.Ok(MessageDefinition.Succeeded);
        }

        public async Task<IApiResponse> UpdateAsync(int id, UpdateExerciseInput input)
        {
            var repo = GetGenericRepository<Exercise>();
            var itemRepo = GetGenericRepository<ExerciseItem>();

            var exercise = await repo.FindWithIncludesAsync(x => x.Id == id, x => x.ExerciseItems);
            if (exercise == null)
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            var oldItems = await itemRepo.GetWithIncludesAsync(i => i.ExerciseId == id, i => i.MediaFile);
            //if (oldItems != null && oldItems.Any())
            //{
            //    foreach (var item in oldItems)
            //    {
            //        foreach (var media in item.MediaLinks)
            //        {
            //            if (!string.IsNullOrWhiteSpace(media.FilePath))
            //            {
            //                await storageService.DeleteFileFromPathAsync(media.FilePath);
            //            }
            //        }
            //    }

            //    itemRepo.DeleteRange(oldItems);
            //}

            exercise.Title = input.Title ?? exercise.Title;
            exercise.TypeId = (int)input.TypeId;
            exercise.ExerciseItems = [];

            foreach (var item in input.Items)
            {
                var newItem = await CreateExerciseItemAsync(item, input.TypeId);
                newItem.ExerciseId = id;
                exercise.ExerciseItems.Add(newItem);
            }

            repo.Update(exercise);
            await CommitAsync();

            return ApiResponse.Ok(new UpdateExerciseOutput
            {
                Input = input,
                Output = Mapper.Map<ExerciseModel>(exercise)
            }, MessageDefinition.Succeeded);
        }
    }

}
