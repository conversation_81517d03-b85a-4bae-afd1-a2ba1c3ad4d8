﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("UserProgresses")]
    public class UserProgress : Entity<int>
    {
        public int? UserId { get; set; }
        public int LessonId { get; set; }
        public float CompletionPercentage { get; set; }
        public DateTimeOffset CompeletedAt { get; set; }
        public int ? Score { get; set; }
        public int Attempts { get; set; } = 0;

        public virtual User? User { get; set; }
        public virtual Lesson? Lesson { get; set; }
    }
}
