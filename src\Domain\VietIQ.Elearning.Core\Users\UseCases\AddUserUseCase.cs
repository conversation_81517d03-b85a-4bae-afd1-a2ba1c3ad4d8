﻿using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using System.Text;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.Core.Extensions;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;
using VietIQ.Elearning.Infrastructure.Email;
using VietIQ.Elearning.Infrastructure.TemplateBuilder;

namespace VietIQ.Elearning.Core.Users
{
    public class AddUserUseCase(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        UserManager<User> userManager,
        IEmailTemplateSender emailSender,
        IWebHostEnvironment environment,
        IConfiguration configuration) : BaseService(unitOfWork, mapper), IAddUserUseCase
    {
        private readonly UserManager<User> userManager = userManager;
        private readonly IEmailTemplateSender emailSender = emailSender;
        private readonly IConfiguration configuration = configuration;

        public async Task<IApiResponse> AddAsync(AddUserInput input)
        {
            // Validate email is existed?
            var userByEmailExist = await userManager.FindByEmailAsync(input.Email);
            if (userByEmailExist != null)
            {
                return ApiResponse.BadRequest(MessageDefinition.AddFailed)
                                    .AppendErrors(nameof(input.Email), MessageDefinition.User.DuplicateEmail);
            }

            // Validate username is existed?
            var userByUsernameExist = await userManager.FindByNameAsync(input.UserName);
            if (userByUsernameExist != null)
            {
                return ApiResponse.BadRequest(MessageDefinition.AddFailed)
                                    .AppendErrors(nameof(input.UserName), MessageDefinition.User.DuplicateUserName);
            }

            // Create user
            var user = Mapper.Map<User>(input);
            using var transaction = CreateTransaction();
            try
            {
                var result = await userManager.CreateAsync(user, user.PasswordHash ?? string.Empty);
                if (!result.Succeeded)
                {
                    return ApiResponse.BadRequest(MessageDefinition.AddFailed)
                                        .AppendErrors(nameof(input.UserName), IdentityErrorMessages.GetUsernameApiMessages(result.Errors))
                                        .AppendErrors(nameof(input.Password), IdentityErrorMessages.GetPasswordErrorMessages(result.Errors))
                                        .AppendErrors("other", IdentityErrorMessages.GetOthernameApiMessages(result.Errors))
                                        .AppendErrors("unknown", IdentityErrorMessages.GetUnknowErrorMessages(result.Errors));
                }

                // Create token default for this user
                var tokenRepository = this.GetGenericRepository<ApiToken>();
                await tokenRepository.AddAsync(new ApiToken()
                {
                    UserId = user.Id,
                    Token = Guid.NewGuid().ToString("N"),
                    IsDefault = true,
                });

                // Create liên kết user với ROLE
                if (input.RoleNames.SafeAny())
                {
                    // TODO: Chỗ này roleName chưa được validate, có thể sẽ xảy ra case RoleName không tồn tại
                    await userManager.AddToRolesAsync(user, input.RoleNames!);
                }

                await this.CommitAsync();
                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }

            if (configuration.GetValue<bool>(SettingKey.ConfirmAccount.Enable))
            {
                await SendConfirmEmailAsync(user);
            }

            if (configuration.GetValue<bool>(SettingKey.InformationAccount.Enable))
            {
                await SendInfoEmailAsync(user, input.Password);
            }

            // Return response
            var userOutput = await userManager.FindByEmailAsync(user.Email ?? string.Empty);
            var userOutputModel = Mapper.Map<UserModel>(userOutput);
            return ApiResponse.Ok(userOutputModel, MessageDefinition.Succeeded);
        }

        private async Task SendInfoEmailAsync(User user, string password)
        {
            var emailTitle = configuration.GetValue<string>(SettingKey.InformationAccount.EmailTile) ?? "Account Information";
            var emailMessage = new EmailMessage(emailTitle, new Recipient($"{user.FirstName} {user.LastName}", user.Email ?? string.Empty));
            await emailSender.SendAsync(emailMessage, options =>
            {
                options.Name = SettingKey.InformationAccount.EmailTemplate;
                options.Type = TemplateType.File;
                options.Builder = (builder) =>
                {
                    builder.Append("${USERNAME}", user.UserName ?? string.Empty);
                    builder.Append("${PASSWORD}", password);
                };
            });
        }

        private async Task SendConfirmEmailAsync(User user)
        {
            var emailTitle = configuration.GetValue<string>(SettingKey.ConfirmAccount.EmailTile) ?? "New Title";
            var emailMessage = new EmailMessage(emailTitle,
                                                new Recipient($"{user.FirstName} {user.LastName}", user.Email ?? string.Empty));
            var confirmUrl = await GetConfirmEmailUrlAsync(user);
            await emailSender.SendAsync(emailMessage, options =>
            {
                options.Name = SettingKey.ConfirmAccount.EmailTemplate;
                options.Type = TemplateType.File;
                options.Builder = (builder) =>
                {
                    builder.Append("${USERNAME}", user.UserName ?? string.Empty);
                    builder.Append("${CONFIRM_URL}", GetConfirmEmailUrlAsync(user));
                };
            });
        }

        private async Task<string> GetConfirmEmailUrlAsync(User user)
        {
            var code = await userManager.GenerateEmailConfirmationTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

            return $"{configuration[SettingKey.ConfirmAccount.UrlFrontend]}?userid={user.Id}&code={code}";
        }
    }
}
