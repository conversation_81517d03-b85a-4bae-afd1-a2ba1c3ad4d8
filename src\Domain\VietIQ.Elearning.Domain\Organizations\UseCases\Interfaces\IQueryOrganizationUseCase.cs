﻿using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;

namespace VietIQ.Elearning.Domain.Organizations
{
    public interface IQueryOrganizationUseCase
    {
        Task<IApiResponse> GetAsync(IPaginationInput paginationInput);

        Task<IApiResponse> GetAllAsync();

        Task<IApiResponse> GetOrganizationByIdAsync(int categoryId);

        Task<IApiResponse> GetClassesByOrganizationIdAsync(int organizationId);

        Task<IApiResponse> GetUsersByOrganizationIdAsync(int organizationId);
    }
}
