﻿using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Core.SeedData
{
    internal class OrganizationSeeder : BaseDataSeeder
    {
        public OrganizationSeeder(ApplicationDbContext context, bool isDevelopment)
            : base(context, isDevelopment)
        {
        }

        public void SeedData()
        {
            var dbSet = this.Context.Set<Organization>();
            if (dbSet != null && !dbSet.Any() && IsDevelopment)
            {
                dbSet.Add(new Organization
                {
                    Id = 1,
                    Code = "TH1",
                    Name = "English Learning Center",
                    Type = "language_school",
                    Status = StatusType.Active,
                });

                this.Context.SaveChanges();
            }
        }
    }
}
