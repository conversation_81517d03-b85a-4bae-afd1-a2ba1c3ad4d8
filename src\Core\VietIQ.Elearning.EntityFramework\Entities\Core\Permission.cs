﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Core_Permissions")]
    public class Permission : AuditEntity<Guid>
    {
        [Required]
        [MaxLength(EntityLength.Normal)]
        public string Name { get; set; } = null!;

        [MaxLength(EntityLength.Normal)]
        public string Discriminator { get; set; } = null!;

        public bool IsGranted { get; set; }

        public int? RoleId { get; set; }

        public int? UserId { get; set; }

        public virtual Role? Role { get; set; }

        public virtual User? User { get; set; }
    }
}
