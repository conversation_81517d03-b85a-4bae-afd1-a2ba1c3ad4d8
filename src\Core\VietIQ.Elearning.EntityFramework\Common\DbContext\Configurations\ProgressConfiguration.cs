﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.EntityFramework
{
    public class ProgressConfiguration : IEntityTypeConfiguration<UserProgress>
    {
        public void Configure(EntityTypeBuilder<UserProgress> builder)
        {
            builder.HasKey(p => p.Id);

            builder.HasIndex(p => new { p.UserId, p.LessonId })
                .HasDatabaseName("IX_Progress_UserLesson")
                .IsUnique();
        }
    }
}
