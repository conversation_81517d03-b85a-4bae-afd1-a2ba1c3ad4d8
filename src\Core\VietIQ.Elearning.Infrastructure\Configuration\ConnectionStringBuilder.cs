﻿using Microsoft.Extensions.Configuration;

namespace VietIQ.Elearning.Infrastructure
{
    public static class ConnectionStringBuilder
    {
        public static string BuildPostgreSqlConnectionString(IConfiguration configuration)
        {
            var host = Environment.GetEnvironmentVariable("DB_HOST") ?? "localhost";
            var database = Environment.GetEnvironmentVariable("DB_NAME") ?? "elearning";
            var username = Environment.GetEnvironmentVariable("DB_USER") ?? "admin";
            var password = Environment.GetEnvironmentVariable("DB_PASSWORD") ?? "VN@123456";
            var port = Environment.GetEnvironmentVariable("DB_PORT") ?? "5432";
            var sslMode = Environment.GetEnvironmentVariable("DB_SSL_MODE") ?? "Disable";

            return $"Host={host};Port={port};Database={database};Username={username};Password={password};SSL Mode={sslMode};Trust Server Certificate=true;";
        }

        public static string BuildRedisConnectionString(IConfiguration configuration)
        {
            var host = Environment.GetEnvironmentVariable("REDIS_HOST") ?? "localhost";
            var port = Environment.GetEnvironmentVariable("REDIS_PORT") ?? "6379";
            var password = Environment.GetEnvironmentVariable("REDIS_PASSWORD");
            var ssl = Environment.GetEnvironmentVariable("REDIS_SSL") ?? "true";

            var connectionString = $"{host}:{port},ssl={ssl},abortConnect=false";

            if (!string.IsNullOrEmpty(password))
            {
                connectionString += $",password={password}";
            }

            return connectionString;
        }
    }
}
