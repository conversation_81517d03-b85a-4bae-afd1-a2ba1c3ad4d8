﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Upload;
using VietIQ.Elearning.Domain.ExerciseAnswers;
using VietIQ.Elearning.Domain.ExerciseItems;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Exercises
{
    public abstract class BaseExerciseUseCase : BaseService
    {
        protected readonly IMediaStorageService MediaStorage;

        protected BaseExerciseUseCase(IUnitOfWork unitOfWork, IMapper mapper, IMediaStorageService mediaStorage) : base(unitOfWork, mapper)
        {
            MediaStorage = mediaStorage;
        }

        protected async Task<ExerciseItem> CreateExerciseItemAsync(AddExerciseItemInput input, ExerciseTypeEnum type)
        {
            return new ExerciseItem
            {
                //DisplayType = input.DisplayType,
                //MediaLinks = await CreateMediaAsync(input.Medias),
            };
        }

        protected async Task<List<ExerciseAnswer>> CreateAnswersAsync(List<AddExerciseAnswerInput> answers, ExerciseTypeEnum type)
        {
            var result = new List<ExerciseAnswer>();

            foreach (var answer in answers)
            {
                var entity = new ExerciseAnswer
                {
                    MediaType = answer.Type,
                    Content = answer.Type == MediaType.Text ? answer.Content : null,
                    IsCorrect = answer.IsCorrect,
                    DisplayOrder = answer.DisplayOrder,
                    Explanation = answer.Explanation
                };

                if (type == ExerciseTypeEnum.SentenceOrdering)
                    entity.OrderIndex = answer.OrderIndex ?? answer.DisplayOrder;

                if (type == ExerciseTypeEnum.Matching)
                    entity.MatchKey = answer.MatchKey ?? Guid.NewGuid().ToString();

                if (answer.File != null)
                {
                    var mediaFile = await CreateMediaAsync(answer.File);
                    entity.MediaFile = mediaFile;
                    entity.MediaFileId = mediaFile.Id;
                }

                result.Add(entity);
            }

            return result;
        }

        protected async Task<IEnumerable<ExerciseItem>> CreateExerciseItemsAsync(IEnumerable<AddExerciseItemInput> items)
        {
            var result = new List<ExerciseItem>();

            foreach (var item in items)
            {
                var entity = new ExerciseItem
                {
                    DisplayOrder = item.Order,
                    Content = item.QuestionContent,
                    MediaType = item.File != null ? GetMediaTypeFromFileName(item.File.FileName) : MediaType.Text,
                };

                if (item.File != null)
                {
                    var mediaFile = await CreateMediaAsync(item.File);
                    entity.MediaFile = mediaFile;
                    entity.MediaFileId = mediaFile.Id;
                }

                result.Add(entity);
            }

            return result;
        }

        protected async Task<MediaFile> CreateMediaAsync(IFormFile fileInput)
        {
            var type = GetMediaTypeFromFileName(fileInput.FileName);
            var entity = new MediaFile
            {
                Id = Guid.NewGuid(),
                Type = type,
                FileName = fileInput.FileName,
                FilePath = await MediaStorage.UploadAsync(fileInput, type, folder: "exercise/medias")
            };

            return entity;
        }

        public static MediaType GetMediaTypeFromFileName(string fileName)
        {
            var extension = Path.GetExtension(fileName)?.ToLowerInvariant();

            if (string.IsNullOrWhiteSpace(extension))
            {
                throw new ArgumentException("Invalid file name or missing extension");
            }

            return extension switch
            {
                ".txt" or ".md" or ".rtf" or ".doc" or ".docx" => MediaType.Text,

                ".jpg" or ".jpeg" or ".png" or ".svg" or ".gif" or ".bmp" or ".webp" => MediaType.Image,

                ".mp3" or ".wav" or ".ogg" or ".aac" or ".flac" => MediaType.Audio,

                ".mp4" or ".mov" or ".avi" or ".mkv" or ".webm" => MediaType.Video,

                _ => throw new NotSupportedException($"Unsupported file type: {extension}")
            };
        }
    }
}
