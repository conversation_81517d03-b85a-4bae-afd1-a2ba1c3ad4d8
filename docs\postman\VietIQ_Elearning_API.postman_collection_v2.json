{"info": {"name": "All APIs", "description": "Tất cả API (core + dynamic)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "ApiToken", "item": [{"name": "GET /api/tokens/{userId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/tokens/{userId}", "host": ["{{base_url}}"], "path": ["api", "tokens", "{userId}"]}}}, {"name": "GET /api/tokens/me", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/tokens/me", "host": ["{{base_url}}"], "path": ["api", "tokens", "me"]}}}, {"name": "POST /api/tokens/me", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/tokens/me", "host": ["{{base_url}}"], "path": ["api", "tokens", "me"]}}}, {"name": "PATCH /api/tokens/me/refresh/{apiTokenId}", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/tokens/me/refresh/{apiTokenId}", "host": ["{{base_url}}"], "path": ["api", "tokens", "me", "refresh", "{apiTokenId}"]}}}, {"name": "DELETE /api/tokens/{apiTokenId}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/tokens/{apiTokenId}", "host": ["{{base_url}}"], "path": ["api", "tokens", "{apiTokenId}"]}}}, {"name": "DELETE /api/tokens/me/{apiTokenId}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/tokens/me/{apiTokenId}", "host": ["{{base_url}}"], "path": ["api", "tokens", "me", "{apiTokenId}"]}}}]}, {"name": "Authentication", "item": [{"name": "POST /api/auth/login", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "POST /api/auth/register", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "POST /api/auth/refresh-token", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh-token"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "POST /api/auth/revoke-token", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/revoke-token", "host": ["{{base_url}}"], "path": ["api", "auth", "revoke-token"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "GET /api/caches/all-entries/{token}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/caches/all-entries/{token}", "host": ["{{base_url}}"], "path": ["api", "caches", "all-entries", "{token}"]}}}, {"name": "GET /api/caches/{key}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/caches/{key}", "host": ["{{base_url}}"], "path": ["api", "caches", "{key}"]}}}]}, {"name": "Class", "item": [{"name": "POST /api/classes", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/classes", "host": ["{{base_url}}"], "path": ["api", "classes"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/classes", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/classes", "host": ["{{base_url}}"], "path": ["api", "classes"]}}}, {"name": "PUT /api/classes/{classId}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/classes/{classId}", "host": ["{{base_url}}"], "path": ["api", "classes", "{classId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/classes/{classId}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/classes/{classId}", "host": ["{{base_url}}"], "path": ["api", "classes", "{classId}"]}}}, {"name": "GET /api/classes/{ClassId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/classes/{ClassId}", "host": ["{{base_url}}"], "path": ["api", "classes", "{ClassId}"]}}}]}, {"name": "Course", "item": [{"name": "GET /api/courses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/courses", "host": ["{{base_url}}"], "path": ["api", "courses"]}}}, {"name": "POST /api/courses", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/courses", "host": ["{{base_url}}"], "path": ["api", "courses"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/courses/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/courses/{id}", "host": ["{{base_url}}"], "path": ["api", "courses", "{id}"]}}}, {"name": "PUT /api/courses/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/courses/{id}", "host": ["{{base_url}}"], "path": ["api", "courses", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/courses/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/courses/{id}", "host": ["{{base_url}}"], "path": ["api", "courses", "{id}"]}}}]}, {"name": "Exercise", "item": [{"name": "GET /api/exercises", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/exercises", "host": ["{{base_url}}"], "path": ["api", "exercises"]}}}, {"name": "POST /api/exercises", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/exercises", "host": ["{{base_url}}"], "path": ["api", "exercises"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/exercises/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/exercises/{id}", "host": ["{{base_url}}"], "path": ["api", "exercises", "{id}"]}}}, {"name": "PUT /api/exercises/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/exercises/{id}", "host": ["{{base_url}}"], "path": ["api", "exercises", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/exercises/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/exercises/{id}", "host": ["{{base_url}}"], "path": ["api", "exercises", "{id}"]}}}]}, {"name": "Lesson", "item": [{"name": "GET /api/lessons", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lessons", "host": ["{{base_url}}"], "path": ["api", "lessons"]}}}, {"name": "POST /api/lessons", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/lessons", "host": ["{{base_url}}"], "path": ["api", "lessons"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/lessons/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lessons/{id}", "host": ["{{base_url}}"], "path": ["api", "lessons", "{id}"]}}}, {"name": "PUT /api/lessons/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/lessons/{id}", "host": ["{{base_url}}"], "path": ["api", "lessons", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/lessons/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/lessons/{id}", "host": ["{{base_url}}"], "path": ["api", "lessons", "{id}"]}}}]}, {"name": "Notification", "item": [{"name": "POST /api/notifications", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/notifications", "host": ["{{base_url}}"], "path": ["api", "notifications"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/notifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/notifications", "host": ["{{base_url}}"], "path": ["api", "notifications"]}}}, {"name": "GET /api/notifications/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/notifications/{id}", "host": ["{{base_url}}"], "path": ["api", "notifications", "{id}"]}}}, {"name": "DELETE /api/notifications/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/notifications/{id}", "host": ["{{base_url}}"], "path": ["api", "notifications", "{id}"]}}}, {"name": "PUT /api/notifications/{id}/read", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/notifications/{id}/read", "host": ["{{base_url}}"], "path": ["api", "notifications", "{id}", "read"]}}}, {"name": "PUT /api/notifications/{id}/read-all", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/notifications/{id}/read-all", "host": ["{{base_url}}"], "path": ["api", "notifications", "{id}", "read-all"]}}}]}, {"name": "OperationLog", "item": [{"name": "POST /api/operation-log", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/operation-log", "host": ["{{base_url}}"], "path": ["api", "operation-log"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/operation-log", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/operation-log", "host": ["{{base_url}}"], "path": ["api", "operation-log"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/operation-log/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/operation-log/{id}", "host": ["{{base_url}}"], "path": ["api", "operation-log", "{id}"]}}}, {"name": "DELETE /api/operation-log/clean", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/operation-log/clean", "host": ["{{base_url}}"], "path": ["api", "operation-log", "clean"]}}}]}, {"name": "Organization", "item": [{"name": "POST /api/organizations", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/organizations", "host": ["{{base_url}}"], "path": ["api", "organizations"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/organizations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/organizations", "host": ["{{base_url}}"], "path": ["api", "organizations"]}}}, {"name": "PUT /api/organizations/{organizationId}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/organizations/{organizationId}", "host": ["{{base_url}}"], "path": ["api", "organizations", "{organizationId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/organizations/{organizationId}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/organizations/{organizationId}", "host": ["{{base_url}}"], "path": ["api", "organizations", "{organizationId}"]}}}, {"name": "GET /api/organizations/{organizationId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/organizations/{organizationId}", "host": ["{{base_url}}"], "path": ["api", "organizations", "{organizationId}"]}}}]}, {"name": "Permission", "item": [{"name": "GET /api/permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/permissions", "host": ["{{base_url}}"], "path": ["api", "permissions"]}}}]}, {"name": "Role", "item": [{"name": "GET /api/roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/roles", "host": ["{{base_url}}"], "path": ["api", "roles"]}}}, {"name": "POST /api/roles", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/roles", "host": ["{{base_url}}"], "path": ["api", "roles"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/roles/all", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/roles/all", "host": ["{{base_url}}"], "path": ["api", "roles", "all"]}}}, {"name": "GET /api/roles/me/granted", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/roles/me/granted", "host": ["{{base_url}}"], "path": ["api", "roles", "me", "granted"]}}}, {"name": "GET /api/roles/{userId}/granted", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/roles/{userId}/granted", "host": ["{{base_url}}"], "path": ["api", "roles", "{userId}", "granted"]}}}, {"name": "PUT /api/roles/{roleId}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/roles/{roleId}", "host": ["{{base_url}}"], "path": ["api", "roles", "{roleId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/roles/{roleId}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/roles/{roleId}", "host": ["{{base_url}}"], "path": ["api", "roles", "{roleId}"]}}}, {"name": "PATCH /api/roles/{roleId}/default", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/roles/{roleId}/default", "host": ["{{base_url}}"], "path": ["api", "roles", "{roleId}", "default"]}}}]}, {"name": "RolePermission", "item": [{"name": "GET /api/permissions/role/{roleId}/tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/permissions/role/{roleId}/tree", "host": ["{{base_url}}"], "path": ["api", "permissions", "role", "{roleId}", "tree"]}}}, {"name": "POST /api/permissions/role/{roleId}", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/permissions/role/{roleId}", "host": ["{{base_url}}"], "path": ["api", "permissions", "role", "{roleId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Session", "item": [{"name": "GET /api/sessions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/sessions", "host": ["{{base_url}}"], "path": ["api", "sessions"]}}}, {"name": "POST /api/sessions", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/sessions", "host": ["{{base_url}}"], "path": ["api", "sessions"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/sessions/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/sessions/{id}", "host": ["{{base_url}}"], "path": ["api", "sessions", "{id}"]}}}, {"name": "PUT /api/sessions/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/sessions/{id}", "host": ["{{base_url}}"], "path": ["api", "sessions", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/sessions/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/sessions/{id}", "host": ["{{base_url}}"], "path": ["api", "sessions", "{id}"]}}}]}, {"name": "Setting", "item": [{"name": "GET /api/system/settings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/system/settings", "host": ["{{base_url}}"], "path": ["api", "system", "settings"]}}}, {"name": "POST /api/system/settings", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/system/settings", "host": ["{{base_url}}"], "path": ["api", "system", "settings"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "POST /api/system/settings/update-busy-flag", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/system/settings/update-busy-flag", "host": ["{{base_url}}"], "path": ["api", "system", "settings", "update-busy-flag"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Subscription", "item": [{"name": "GET /api/Subscription", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/Subscription", "host": ["{{base_url}}"], "path": ["api", "Subscription"]}}}, {"name": "POST /api/Subscription", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/Subscription", "host": ["{{base_url}}"], "path": ["api", "Subscription"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/Subscription/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/Subscription/{id}", "host": ["{{base_url}}"], "path": ["api", "Subscription", "{id}"]}}}, {"name": "PUT /api/Subscription/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/Subscription/{id}", "host": ["{{base_url}}"], "path": ["api", "Subscription", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/Subscription/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/Subscription/{id}", "host": ["{{base_url}}"], "path": ["api", "Subscription", "{id}"]}}}]}, {"name": "SubscriptionClass", "item": [{"name": "GET /api/SubscriptionClass/by-subscription/{subscriptionId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/SubscriptionClass/by-subscription/{subscriptionId}", "host": ["{{base_url}}"], "path": ["api", "SubscriptionClass", "by-subscription", "{subscriptionId}"]}}}, {"name": "POST /api/SubscriptionClass", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/SubscriptionClass", "host": ["{{base_url}}"], "path": ["api", "SubscriptionClass"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/SubscriptionClass", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/SubscriptionClass", "host": ["{{base_url}}"], "path": ["api", "SubscriptionClass"]}}}]}, {"name": "SubscriptionUser", "item": [{"name": "GET /api/SubscriptionUser/by-subscription/{subscriptionId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/SubscriptionUser/by-subscription/{subscriptionId}", "host": ["{{base_url}}"], "path": ["api", "SubscriptionUser", "by-subscription", "{subscriptionId}"]}}}, {"name": "POST /api/SubscriptionUser", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/SubscriptionUser", "host": ["{{base_url}}"], "path": ["api", "SubscriptionUser"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/SubscriptionUser", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/SubscriptionUser", "host": ["{{base_url}}"], "path": ["api", "SubscriptionUser"]}}}]}, {"name": "Tag", "item": [{"name": "GET /api/Tag", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/Tag", "host": ["{{base_url}}"], "path": ["api", "Tag"]}}}, {"name": "POST /api/Tag", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/Tag", "host": ["{{base_url}}"], "path": ["api", "Tag"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/Tag/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/Tag/{id}", "host": ["{{base_url}}"], "path": ["api", "Tag", "{id}"]}}}, {"name": "PUT /api/Tag/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/Tag/{id}", "host": ["{{base_url}}"], "path": ["api", "Tag", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/Tag/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/Tag/{id}", "host": ["{{base_url}}"], "path": ["api", "Tag", "{id}"]}}}]}, {"name": "TestAppService", "item": [{"name": "POST /api/test/add", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/test/add", "host": ["{{base_url}}"], "path": ["api", "test", "add"]}}}]}, {"name": "User", "item": [{"name": "POST /api/users", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}}}, {"name": "PUT /api/users/{userId}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/users/{userId}", "host": ["{{base_url}}"], "path": ["api", "users", "{userId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/users/{userId}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/users/{userId}", "host": ["{{base_url}}"], "path": ["api", "users", "{userId}"]}}}, {"name": "PUT /api/users/with-file/{userId}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/users/with-file/{userId}", "host": ["{{base_url}}"], "path": ["api", "users", "with-file", "{userId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "PUT /api/users/me", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/users/me", "host": ["{{base_url}}"], "path": ["api", "users", "me"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "PATCH /api/users/{userId}/password", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/users/{userId}/password", "host": ["{{base_url}}"], "path": ["api", "users", "{userId}", "password"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "PATCH /api/users/me/password", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/users/me/password", "host": ["{{base_url}}"], "path": ["api", "users", "me", "password"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "PATCH /api/users/{userId}/max-token", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/users/{userId}/max-token", "host": ["{{base_url}}"], "path": ["api", "users", "{userId}", "max-token"]}}}, {"name": "PATCH /api/users/{userId}/enable", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/users/{userId}/enable", "host": ["{{base_url}}"], "path": ["api", "users", "{userId}", "enable"]}}}, {"name": "PATCH /api/users/enables", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/users/enables", "host": ["{{base_url}}"], "path": ["api", "users", "enables"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "PATCH /api/users/{userId}/verify-email", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/api/users/{userId}/verify-email", "host": ["{{base_url}}"], "path": ["api", "users", "{userId}", "verify-email"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "POST /api/users/delete-all", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/users/delete-all", "host": ["{{base_url}}"], "path": ["api", "users", "delete-all"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/users/{id}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/{id}", "host": ["{{base_url}}"], "path": ["api", "users", "{id}"]}}}, {"name": "GET /api/users/profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}]}, {"name": "UserPermission", "item": [{"name": "GET /api/permissions/me/granted", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/permissions/me/granted", "host": ["{{base_url}}"], "path": ["api", "permissions", "me", "granted"]}}}, {"name": "GET /api/permissions/me/tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/permissions/me/tree", "host": ["{{base_url}}"], "path": ["api", "permissions", "me", "tree"]}}}, {"name": "GET /api/permissions/user/{userId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/permissions/user/{userId}", "host": ["{{base_url}}"], "path": ["api", "permissions", "user", "{userId}"]}}}, {"name": "POST /api/permissions/user/{userId}", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/permissions/user/{userId}", "host": ["{{base_url}}"], "path": ["api", "permissions", "user", "{userId}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Version", "item": [{"name": "GET /api/version", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/version", "host": ["{{base_url}}"], "path": ["api", "version"]}}}]}, {"name": "Week", "item": [{"name": "GET /api/weeks", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weeks", "host": ["{{base_url}}"], "path": ["api", "weeks"]}}}, {"name": "POST /api/weeks", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/weeks", "host": ["{{base_url}}"], "path": ["api", "weeks"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GET /api/weeks/{weekId}", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weeks/{weekId}", "host": ["{{base_url}}"], "path": ["api", "weeks", "{weekId}"]}}}, {"name": "PUT /api/weeks/{id}", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/weeks/{id}", "host": ["{{base_url}}"], "path": ["api", "weeks", "{id}"]}, "body": {"mode": "raw", "raw": "{\r\n  \"example_field\": \"value\"\r\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DELETE /api/weeks/{id}", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/weeks/{id}", "host": ["{{base_url}}"], "path": ["api", "weeks", "{id}"]}}}]}]}