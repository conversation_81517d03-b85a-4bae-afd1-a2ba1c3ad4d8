﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Lessons
{
    public class QueryLessonsByWeekIdUseCase(IUnitOfWork unitOfWork,
                                                IMapper mapper) : BaseService(unitOfWork, mapper), IQueryLessonsByWeekIdUseCase
    {
        public async Task<IApiResponse> ExecuteAsync(int weekId)
        {
            var repo = GetGenericRepository<Lesson>();
            var predicate = PredicateBuilder.False<Lesson>();
            predicate = predicate.Or(x => x.WeekId == weekId);

            var result = await repo.GetAsync(predicate, x => x.Week);
            if (result == null || !result.Any())
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<IEnumerable<LessonModel>>(result));
        }
    }
}
