﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace VietIQ.Elearning.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class Add_Table_After_Refactor_Exercise : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExerciseAnswers_ExerciseItems_ExerciseItemId",
                table: "ExerciseAnswers");

            migrationBuilder.DropTable(
                name: "ExerciseMedias");

            migrationBuilder.DropColumn(
                name: "CorrectAnswers",
                table: "Exercises");

            migrationBuilder.DropColumn(
                name: "CorrectOrder",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "Explanation",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "FillBlankTemplate",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "MatchPairs",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "FilePath",
                table: "ExerciseAnswers");

            migrationBuilder.RenameColumn(
                name: "QuestionContent",
                table: "ExerciseItems",
                newName: "Content");

            migrationBuilder.RenameColumn(
                name: "DisplayType",
                table: "ExerciseItems",
                newName: "MediaType");

            migrationBuilder.RenameColumn(
                name: "DisplayDirection",
                table: "ExerciseItems",
                newName: "DisplayOrder");

            migrationBuilder.RenameColumn(
                name: "Type",
                table: "ExerciseAnswers",
                newName: "MediaType");

            migrationBuilder.RenameColumn(
                name: "ExerciseItemId",
                table: "ExerciseAnswers",
                newName: "ExerciseId");

            migrationBuilder.RenameIndex(
                name: "IX_ExerciseAnswers_ExerciseItemId",
                table: "ExerciseAnswers",
                newName: "IX_ExerciseAnswers_ExerciseId");

            migrationBuilder.AlterColumn<string>(
                name: "QuestionData",
                table: "Exercises",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "Exercises",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<int>(
                name: "DisplayDirection",
                table: "Exercises",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Explanation",
                table: "Exercises",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MediaFileId",
                table: "ExerciseItems",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MatchKey",
                table: "ExerciseAnswers",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MediaFileId",
                table: "ExerciseAnswers",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MediaFiles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    FilePath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    FileName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MediaFiles", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExerciseItems_MediaFileId",
                table: "ExerciseItems",
                column: "MediaFileId");

            migrationBuilder.CreateIndex(
                name: "IX_ExerciseAnswers_MediaFileId",
                table: "ExerciseAnswers",
                column: "MediaFileId");

            migrationBuilder.AddForeignKey(
                name: "FK_ExerciseAnswers_Exercises_ExerciseId",
                table: "ExerciseAnswers",
                column: "ExerciseId",
                principalTable: "Exercises",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ExerciseAnswers_MediaFiles_MediaFileId",
                table: "ExerciseAnswers",
                column: "MediaFileId",
                principalTable: "MediaFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ExerciseItems_MediaFiles_MediaFileId",
                table: "ExerciseItems",
                column: "MediaFileId",
                principalTable: "MediaFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExerciseAnswers_Exercises_ExerciseId",
                table: "ExerciseAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_ExerciseAnswers_MediaFiles_MediaFileId",
                table: "ExerciseAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_ExerciseItems_MediaFiles_MediaFileId",
                table: "ExerciseItems");

            migrationBuilder.DropTable(
                name: "MediaFiles");

            migrationBuilder.DropIndex(
                name: "IX_ExerciseItems_MediaFileId",
                table: "ExerciseItems");

            migrationBuilder.DropIndex(
                name: "IX_ExerciseAnswers_MediaFileId",
                table: "ExerciseAnswers");

            migrationBuilder.DropColumn(
                name: "DisplayDirection",
                table: "Exercises");

            migrationBuilder.DropColumn(
                name: "Explanation",
                table: "Exercises");

            migrationBuilder.DropColumn(
                name: "MediaFileId",
                table: "ExerciseItems");

            migrationBuilder.DropColumn(
                name: "MediaFileId",
                table: "ExerciseAnswers");

            migrationBuilder.RenameColumn(
                name: "MediaType",
                table: "ExerciseItems",
                newName: "DisplayType");

            migrationBuilder.RenameColumn(
                name: "DisplayOrder",
                table: "ExerciseItems",
                newName: "DisplayDirection");

            migrationBuilder.RenameColumn(
                name: "Content",
                table: "ExerciseItems",
                newName: "QuestionContent");

            migrationBuilder.RenameColumn(
                name: "MediaType",
                table: "ExerciseAnswers",
                newName: "Type");

            migrationBuilder.RenameColumn(
                name: "ExerciseId",
                table: "ExerciseAnswers",
                newName: "ExerciseItemId");

            migrationBuilder.RenameIndex(
                name: "IX_ExerciseAnswers_ExerciseId",
                table: "ExerciseAnswers",
                newName: "IX_ExerciseAnswers_ExerciseItemId");

            migrationBuilder.AlterColumn<string>(
                name: "QuestionData",
                table: "Exercises",
                type: "jsonb",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(2000)",
                oldMaxLength: 2000);

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "Exercises",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20);

            migrationBuilder.AddColumn<string>(
                name: "CorrectAnswers",
                table: "Exercises",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CorrectOrder",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Explanation",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FillBlankTemplate",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MatchPairs",
                table: "ExerciseItems",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MatchKey",
                table: "ExerciseAnswers",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FilePath",
                table: "ExerciseAnswers",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ExerciseMedias",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ExerciseItemId = table.Column<int>(type: "integer", nullable: false),
                    Content = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    DisplayType = table.Column<int>(type: "integer", nullable: false),
                    FilePath = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExerciseMedias", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExerciseMedias_ExerciseItems_ExerciseItemId",
                        column: x => x.ExerciseItemId,
                        principalTable: "ExerciseItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExerciseMedias_ExerciseItemId",
                table: "ExerciseMedias",
                column: "ExerciseItemId");

            migrationBuilder.AddForeignKey(
                name: "FK_ExerciseAnswers_ExerciseItems_ExerciseItemId",
                table: "ExerciseAnswers",
                column: "ExerciseItemId",
                principalTable: "ExerciseItems",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
