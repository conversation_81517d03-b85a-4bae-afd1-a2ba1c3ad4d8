﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VietIQ.Elearning.EntityFramework.Entities
{
    [Table("Subscriptions")]
    public class Subscription : AuditEntity<int>
    {
        public int? OrganizationId { get; set; }

        public string Name { get; set; } = null!;

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public StatusType Status { get; set; }

        public virtual Organization? Organization { get; set; }
        public virtual List<SubscriptionUser> SubscriptionUsers { get; set; } = [];
        public virtual List<SubscriptionClass> SubscriptionClasses { get; set; } = [];
    }
}
