﻿using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using static System.Runtime.InteropServices.JavaScript.JSType;


namespace VietIQ.Elearning.JwtBearer
{
    public class JwtProvider(IConfiguration configuration) : IJwtProvider
    {
        private readonly JwtOption jwtOptions = configuration.GetJwtOptions();

        public string GenerateJwtToken(params Claim[] claims)
        {
            var jti = Guid.NewGuid().ToString();
            var allClaims = claims.ToList();
            allClaims.Add(new Claim(JwtRegisteredClaimNames.Jti, jti));
            allClaims.Add(new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64));
            var data = Encoding.UTF8.GetBytes(this.jwtOptions.SecretKey);
            var signingKey = new SymmetricSecurityKey(data);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(allClaims),
                SigningCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256Signature),
                Issuer = jwtOptions.Issuer,
                Audience = jwtOptions.Audience,
                NotBefore = DateTime.UtcNow,
                IssuedAt = DateTime.UtcNow,
            };

            var timeExpired = this.jwtOptions.TimeExpired;
#if DEBUG
            // Tăng thời gian time expired trên môi trường debug
            timeExpired *= 10;
#endif

            // Nếu thời gian hết hạn lớn hơn 0 thì thiết lập
            if (timeExpired > 0)
            {
                tokenDescriptor.Expires = DateTime.UtcNow.AddMinutes(timeExpired);
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateJwtSecurityToken(tokenDescriptor);

            return tokenHandler.WriteToken(token);
        }

        public int GetTimeExpired()
        {
            return this.jwtOptions.TimeExpired;
        }

        public string GenerateRefreshToken(string ipAddress)
        {
            // Create strong random crypto
            var randomArray1 = RandomNumberGenerator.GetBytes(64);
            // Create unique uid
            var randomArray2 = Guid.NewGuid().ToByteArray();
            var randomArray3 = Guid.NewGuid().ToByteArray();

            var newArray = randomArray1.Union(randomArray2).Union(randomArray3).ToArray();
            return Convert.ToBase64String(newArray);
        }

        public string GenerateToken(int userId)
        {
            var key = Encoding.UTF8.GetBytes(jwtOptions.SecretKey);
            var signingKey = new SymmetricSecurityKey(key);
            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(
                [
                    new Claim("userId", userId.ToString())
                ]),
                Expires = DateTime.UtcNow.AddHours(24),
                SigningCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}
