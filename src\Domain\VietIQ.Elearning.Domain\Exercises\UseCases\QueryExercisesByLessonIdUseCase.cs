﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Exercises.UseCases
{
    public class QueryExercisesByLessonIdUseCase(IUnitOfWork unitOfWork,
                                                 IMapper mapper) : BaseService(unitOfWork, mapper), IQueryExercisesByLessonIdUseCase
    {
        public async Task<IApiResponse> ExecuteAsync(int lessonId)
        {
            var repo = GetGenericRepository<Exercise>();
            var entities = await repo.GetAsync(x => x.LessonId == lessonId);
            if (entities == null || !entities.Any())
            {
                return ApiResponse.NotFound(MessageDefinition.NotFound);
            }

            return ApiResponse.Ok(Mapper.Map<IEnumerable<ExerciseModel>>(entities));
        }
    }
}
