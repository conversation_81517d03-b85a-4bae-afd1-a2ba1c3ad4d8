﻿using AutoMapper;
using VietIQ.Elearning.Core.BaseServices;
using VietIQ.Elearning.Core.Constants;
using VietIQ.Elearning.DynamicApi.Response;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.Courses
{
    public class AddCourseUseCase(IUnitOfWork unitOfWork, IMapper mapper) : BaseService(unitOfWork, mapper), IAddCourseUseCase
    {
        public async Task<IApiResponse> AddAsync(AddCourseInput input)
        {
            var repo = GetGenericRepository<Course>();
            var gradeRepo = GetGenericRepository<Grade>();
            var grade = await gradeRepo.FindAsync(x => x.Id == input.GradeId);
            if (grade == null)
            {
                return ApiResponse.NotFound(MessageDefinition.Grade.GradeNotFound);
            }

            var entity = Map<Course>(input);
            await repo.AddAsync(entity);
            await CommitAsync();

            var output = Mapper.Map<CourseModel>(entity);
            return ApiResponse.Ok(output, MessageDefinition.Succeeded);
        }
    }
}
