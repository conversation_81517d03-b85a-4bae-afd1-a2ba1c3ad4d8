﻿using VietIQ.Elearning.Domain.ExerciseAnswers;
using VietIQ.Elearning.Domain.MediaFiles;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Domain.ExerciseItems
{
    public class ExerciseItemModel
    {
        public int Id { get; set; }

        public int ExerciseId { get; set; }

        public DisplayDirection DisplayDirection { get; set; } = DisplayDirection.Vertical;

        public DisplayTypeEnum DisplayType { get; set; } = DisplayTypeEnum.Text;

        public string? QuestionContent { get; set; }

        public string? CorrectOrder { get; set; }

        public string? MatchPairs { get; set; }

        public string? FillBlankTemplate { get; set; }

        public string? Explanation { get; set; }

        public virtual ExerciseItem Exercise { get; set; } = null!;

        public virtual IEnumerable<MediaFileModel> Medias { get; set; } = [];
    }
}
