﻿using VietIQ.Elearning.Api.Shared.SeedData;
using VietIQ.Elearning.EntityFramework;
using VietIQ.Elearning.EntityFramework.Entities;

namespace VietIQ.Elearning.Api.Core.SeedData
{
    class CourseSeeder : BaseDataSeeder
    {
        public CourseSeeder(ApplicationDbContext context, bool isDevelopment) : base(context, isDevelopment)
        {
        }

        public void SeedData()
        {
            var dbSet = this.Context.Set<Course>();
            if (dbSet != null && !dbSet.Any() && IsDevelopment)
            {
                dbSet.AddRange(
                   new Course
                   {
                       Id = 1,
                       GradeId = 1,
                       Title = "English for Beginners",
                       Description = "Basic English course for beginners",
                       CourseType = "language"
                   },
                   new Course
                   {
                       Id = 2,
                       GradeId = 1,
                       Title = "English Intermediate",
                       Description = "Intermediate English course",
                       CourseType = "language"
                   }
                );

                this.Context.SaveChanges();
            }
        }
    }
}
